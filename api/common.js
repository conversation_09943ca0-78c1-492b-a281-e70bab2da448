import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'
import dictMock from '../mock/dictMock'

export default {
  /**
   * 上传图片接口
   * @param {FormData} data 表单数据
   * @returns {Promise} 上传结果
   */
  uploadImage(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/upload/file`,
      method: 'post',
      timeout: 30000,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data
    })
  },

  /**
   * 发货页面专用上传附件接口
   * @param {FormData} data 表单数据
   * @returns {Promise} 上传结果
   */
  uploadShippingAttachment(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/shipping/upload`,
      method: 'post',
      timeout: 30000,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data
    })
  },
  /**
   * 获取图形验证码
   * @returns {Promise} 返回验证码信息，包含验证码图片和验证码ID
   */
  getCaptcha() {
    return request({
      url: `${apiBaseUrl}/v1/common/captcha/generate`,
      method: 'get'
    })
  },
  /**
    * 获取区域列表
    * @param {Object} params - 查询参数
    * @returns {Promise} - 返回请求结果
    */
  getListregion(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/region`,
      method: 'get',
      params
    })
  },
  /**
  * 获取区域树结构
  * @param {Object} params - 查询参数
  * @returns {Promise} - 返回请求结果
  */
  getTreeRegion(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/region/tree`,
      method: 'get',
      params
    });
  },
  
  /**
  * 获取区域树结构（带缓存增强）
  * 该方法会先从本地缓存获取数据，如果缓存不存在或过期才会请求服务器
  * @param {Object} params - 查询参数
  * @param {boolean} params.forceRefresh - 是否强制刷新，不使用本地缓存
  * @returns {Promise} - 返回请求结果
  */
  getTreeRegionWithCache(params = {}) {
    // 本地存储的键名
    const STORAGE_KEY = 'region_tree_data';
    const { forceRefresh, ...restParams } = params;
    
    // 如果不是强制刷新，尝试从本地存储获取
    if (!forceRefresh) {
      try {
        const cachedData = localStorage.getItem(STORAGE_KEY);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          // 检查数据是否有效且未过期（默认缓存24小时）
          const now = new Date().getTime();
          if (parsedData && parsedData.timestamp && (now - parsedData.timestamp < 24 * 60 * 60 * 1000)) {
            console.log('从本地存储获取地区树数据');
            return Promise.resolve(parsedData.data);
          }
        }
      } catch (error) {
        console.error('从本地存储获取地区树数据失败:', error);
        // 如果获取本地数据失败，继续从服务器获取
      }
    }
    
    // 从服务器获取数据，调用原始方法
    return this.getTreeRegion(restParams).then(response => {
      try {
        if (response && response.code === 200) {
          const dataToCache = {
            timestamp: new Date().getTime(),
            data: response
          };
          localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToCache));
          console.log('地区树数据已保存到本地存储');
        }
      } catch (error) {
        console.error('保存地区树数据到本地存储失败:', error);
      }
      return response;
    });
  },
  /**
   * 获取快递公司编码列表
   * @param {Object} params - 查询参数，包含分页和搜索条件
   * @param {number} params.page - 当前页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为10
   * @param {string} params.company_name - 快递公司名称，支持模糊搜索
   * @returns {Promise} - 返回请求结果
   */
  getExpressCompanyCode(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/express-company-code`,
      method: 'get',
      params
    })
  },
  /**
   * 发送留言板
   * @param {Object} data - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  messageBoard(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-board`,
      method: 'post',
      data
    })
  },
  /**
   * 发送短信
   * @returns {Promise} - 返回请求结果
   */
  sendSms(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/sms/send`,
      method: 'post',
      data
    })
  },
  sendEmail(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/email/send`,
      method: 'post',
      data
    })
  },
}
