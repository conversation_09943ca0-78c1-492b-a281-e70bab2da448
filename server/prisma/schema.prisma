generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["master", "merchant", "supplier", "base", "official", "spider", "provider", "csm", "crm", "finance"]
}


// ../apps/crm/prisma/models/base.prisma





// ../apps/crm/prisma/models/customers.prisma
// CRM 客户管理相关模型

// 客户基本信息表
model CrmCustomer {
  id              BigInt   @id // 客户ID（年月日时分秒+两位递增数字）
  customerName    String   @map("customer_name") // 客户名称（必填）
  customerAlias   String?  @map("customer_alias") // 客户简称
  parentGroup     String?  @map("parent_group") // 所属集团
  source          Int      // 客户来源（必填）：0-主动获取，1-推荐介绍
  industry        Int?     // 所属行业：0-互联网，1-金融，2-教育，3-医疗，4-制造业，5-服务业
  status          Int      // 客户状态（必填）：0-在营，1-暂停营业，2-关闭
  companyScale    String?  @map("company_scale") // 企业规模
  provinceId      String?  @map("province_id") // 省份ID
  cityId          String?  @map("city_id") // 城市ID
  districtId      String?  @map("district_id") // 区县ID
  detailAddress   String?  @map("detail_address") // 详细地址
  remark          String?  // 客户备注

  // 新增字段
  orderAccount    String?  @map("order_account") // 下单账号（可选）
  orderPlatformId BigInt?  @map("order_platform_id") // 下单平台（渠道表ID）
  customerLevel   Int?     @map("customer_level") // 客户等级：1-A重点客户，2-B潜力客户，3-C开发客户，4-普通客户（有价值需产出），5-E普通客户（有价值无产出）
  enterpriseNature Int?    @map("enterprise_nature") // 企业性质：1-央企，2-国企，3-私人企业，4-港澳企业，5-日资企业，6-美资企业，7-台资企业，8-合资企业，9-外资企业（其他国家）
  businessScope   String?  @map("business_scope") // 经营范围
  topThreePeers   String?  @map("top_three_peers") // 同行前三
  quotationNotes  String?  @map("quotation_notes") // 报价注意事项
  listingNotes    String?  @map("listing_notes") // 上架注意事项
  shippingNotes   String?  @map("shipping_notes") // 发货注意事项

  // 业务员关联字段
  salespersonId   String?  @map("salesperson_id") // 负责业务员ID（逗号分隔的多个ID）

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  financeInfo     CrmCustomerFinanceInfo?
  invoiceInfos    CrmCustomerInvoiceInfo[]
  contacts        CrmCustomerContact[]
  attachments     CrmCustomerAttachment[]
  orders          CrmCustomerOrder[]

  @@map("customers")
  @@schema("crm")
}

// 客户财务信息表
model CrmCustomerFinanceInfo {
  id                    BigInt   @id // 雪花ID
  customerId            BigInt   @unique @map("customer_id") // 客户ID
  accountHolderName     String?  @map("account_holder_name") // 开户人名称
  bankName              String?  @map("bank_name") // 开户行
  bankAccount           String?  @map("bank_account") // 银行账号
  accountPeriodBasis    Int?     @map("account_period_basis") // 账期依据：0-订单已发货，1-订单已收货，2-订单已开票
  settlementMethod      Int?     @map("settlement_method") // 结算方式：0-月结，1-固定账期，2-现销现结
  settlementDate        String?  @map("settlement_date") // 结算日期

  // 系统字段 - 使用毫秒时间戳
  createdAt             BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt             BigInt   @map("updated_at")
  deletedAt             BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy             BigInt?  @map("created_by") // 创建人
  updatedBy             BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer              CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_finance_info")
  @@schema("crm")
}

// 客户开票信息表（多个）
model CrmCustomerInvoiceInfo {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  invoiceTitle    String   @map("invoice_title") // 发票抬头名称（必填）
  taxNumber       String?  @map("tax_number") // 税号
  companyAddress  String?  @map("company_address") // 单位地址
  companyPhone    String?  @map("company_phone") // 公司电话
  bankName        String?  @map("bank_name") // 开户银行
  bankAccount     String?  @map("bank_account") // 银行账号

  // 新增字段
  invoiceAddress  String?  @map("invoice_address") // 开票地址
  invoicePhone    String?  @map("invoice_phone") // 开票电话

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_invoice_info")
  @@schema("crm")
}

// 客户联系人信息表（多个）
model CrmCustomerContact {
  id              BigInt   @id // 雪花ID
  customerId      BigInt?  @map("customer_id") // 客户ID（可为空，支持独立联系人）
  contactName     String   @map("contact_name") // 联系人姓名（必填）
  contactPhone    String   @map("contact_phone") // 联系电话（必填）
  wechatId        String?  @map("wechat_id") // 微信号
  email           String?  // 电子邮箱
  department      String?  // 所属部门
  position        String?  // 公司职位
  birthday        BigInt?  // 生日日期（时间戳）
  provinceId      String?  @map("province_id") // 省份ID
  cityId          String?  @map("city_id") // 城市ID
  districtId      String?  @map("district_id") // 区县ID
  detailAddress   String?  @map("detail_address") // 详细地址
  isDefault       Boolean  @default(false) @map("is_default") // 默认联系人（必填）
  remark          String?  // 备注

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系（可选，支持独立联系人）
  customer        CrmCustomer? @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_contacts")
  @@schema("crm")
}

// 客户附件表（多图）
model CrmCustomerAttachment {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  fileName        String?  @map("file_name") // 文件名（可选）
  fileUrl         String   @map("file_url") // 文件URL
  fileSize        Int?     @map("file_size") // 文件大小（字节）
  fileType        String?  @map("file_type") // 文件类型

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_attachments")
  @@schema("crm")
}

// 客户关联订单表
model CrmCustomerOrder {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  orderId         BigInt   @map("order_id") // 订单ID
  orderNumber     String?  @map("order_number") // 订单号（冗余字段，便于查询）
  isTemporary     Int      @default(0) @map("is_temporary") // 是否为临时客户关联：0-正式客户 1-临时客户

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 软删除时间戳
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 添加唯一索引，确保一个订单只能关联一个客户
  @@unique([orderId])
  @@index([customerId])
  @@index([orderNumber])
  @@map("customer_orders")
  @@schema("crm")
}

// CRM联系人订单关联表
model CrmContactOrder {
  id              BigInt   @id // 雪花ID
  contactId       BigInt   @map("contact_id") // 联系人ID
  orderId         BigInt   @map("order_id") // 订单ID
  orderNumber     String?  @map("order_number") // 订单号（冗余字段）

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 添加索引
  @@unique([orderId])
  @@index([contactId])
  @@index([orderNumber])
  @@map("contact_orders")
  @@schema("crm")
}

// ../apps/crm/prisma/models/invoice_headers.prisma
/// CRM发票抬头表，存储CRM模块用户的发票抬头信息
model CrmInvoiceHeader {
  // 主键
  id            BigInt    @id @default(autoincrement()) /// 发票抬头ID，主键
  
  // 关联字段
  user_id       BigInt                                  /// 用户ID，关联base.system_user表
  
  // 发票抬头基本信息
  name          String    @db.VarChar(100)              /// 发票抬头名称，必填
  tax_number    String?   @db.VarChar(50)               /// 纳税人识别号
  phone         String?   @db.VarChar(20)               /// 公司电话
  address       String?   @db.VarChar(200)              /// 公司地址
  
  // 银行信息
  bank_name     String?   @db.VarChar(100)              /// 开户银行名称
  bank_account  String?   @db.VarChar(30)               /// 银行账号
  
  // 状态信息
  is_default    Boolean   @default(false)               /// 是否为默认抬头
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 索引
  @@index([user_id], name: "idx_crm_invoice_headers_user_id")
  @@index([name], name: "idx_crm_invoice_headers_name")
  @@index([is_default], name: "idx_crm_invoice_headers_is_default")
  @@index([status], name: "idx_crm_invoice_headers_status")
  @@index([deleted_at], name: "idx_crm_invoice_headers_deleted_at")
  
  @@map("invoice_headers")
  @@schema("crm")
}


// ../apps/crm/prisma/models/order_assignment.prisma
// CRM订单分配表 - 用于直销模块订单分配给内部用户
model CrmOrderAssignment {
  // 主键
  id                  BigInt   @id @default(autoincrement()) /// 分配记录ID，自增长

  // 关联字段
  orderId             BigInt   @map("order_id") /// 订单ID，关联base.orders表
  orderReportId       BigInt?  @map("order_report_id") /// 报备信息ID（可选）
  assignedUserId      BigInt   @map("assigned_user_id") /// 被分配的内部用户ID，关联base.system_user表

  // 分配信息
  rate                Decimal  @db.Decimal(10, 4) /// 费率，小数格式
  assignmentAmount    Decimal? @map("assignment_amount") @db.Decimal(18, 2) /// 分配金额
  assignmentStatus    Int      @default(1) @map("assignment_status") /// 分配状态：1-已分配，2-已接受，3-已拒绝，4-已完成
  remark              String?  @db.Text /// 备注信息

  // 操作信息
  assignedBy          BigInt   @map("assigned_by") /// 分配人ID
  assignedAt          BigInt   @map("assigned_at") /// 分配时间戳（毫秒）
  acceptedAt          BigInt?  @map("accepted_at") /// 接受时间戳（毫秒）
  completedAt         BigInt?  @map("completed_at") /// 完成时间戳（毫秒）

  // 系统字段 - 使用毫秒时间戳
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([orderId], map: "idx_crm_order_assignment_order_id")
  @@index([orderReportId], map: "idx_crm_order_assignment_order_report_id")
  @@index([assignedUserId], map: "idx_crm_order_assignment_assigned_user_id")
  @@index([assignmentStatus], map: "idx_crm_order_assignment_status")
  @@index([assignedAt], map: "idx_crm_order_assignment_assigned_at")
  @@index([deletedAt], map: "idx_crm_order_assignment_deleted_at")
  @@map("order_assignment")
  @@schema("crm")
}


// ../apps/crm/prisma/models/payment_recognition_application.prisma
// CRM申请认款相关模型

// 申请认款记录表
model CrmPaymentRecognitionApplication {
  // 主键
  id                  BigInt   @id /// 申请记录ID，雪花ID

  // 申请信息
  applicationSn       String   @unique @map("application_sn") @db.VarChar(64) /// 申请编号，自动生成，格式：CRM-PRA-YYYYMMDD-XXX
  applicantId         BigInt   @map("applicant_id") /// 申请人ID
  totalAmount         Decimal  @map("total_amount") @db.Decimal(13, 2) /// 申请认款总金额
  paymentDate         BigInt   @map("payment_date") /// 付款日期（毫秒时间戳）
  description         String?  @db.Text /// 备注说明

  // 状态信息
  status              Int      @default(0) /// 申请状态：0-待审核，1-审核通过，2-审核驳回
  auditorId           BigInt?  @map("auditor_id") /// 审核人ID
  auditTime           BigInt?  @map("audit_time") /// 审核时间（毫秒时间戳）
  auditRemark         String?  @map("audit_remark") @db.Text /// 审核备注

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  orders              CrmPaymentRecognitionOrder[]      /// 关联的订单
  vouchers            CrmPaymentRecognitionVoucher[]    /// 关联的付款凭证
  attachments         CrmPaymentRecognitionAttachment[] /// 关联的附件

  // 索引
  @@index([applicantId], map: "idx_crm_payment_recognition_application_applicant_id")
  @@index([status], map: "idx_crm_payment_recognition_application_status")
  @@index([paymentDate], map: "idx_crm_payment_recognition_application_payment_date")
  @@index([createdAt], map: "idx_crm_payment_recognition_application_created_at")
  @@index([deletedAt], map: "idx_crm_payment_recognition_application_deleted_at")
  @@map("payment_recognition_applications")
  @@schema("crm")
}

// 申请认款订单关联表
model CrmPaymentRecognitionOrder {
  // 主键
  id                  BigInt   @id /// 关联记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID
  orderId             BigInt   @map("order_id") /// 订单ID
  orderAmount         Decimal  @map("order_amount") @db.Decimal(13, 2) /// 订单金额

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_order_application_id")
  @@index([orderId], map: "idx_crm_payment_recognition_order_order_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_order_deleted_at")
  @@map("payment_recognition_orders")
  @@schema("crm")
}

// 申请认款付款凭证关联表
model CrmPaymentRecognitionVoucher {
  // 主键
  id                  BigInt   @id /// 关联记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID
  voucherId           BigInt   @map("voucher_id") /// 付款凭证ID

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_voucher_application_id")
  @@index([voucherId], map: "idx_crm_payment_recognition_voucher_voucher_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_voucher_deleted_at")
  @@map("payment_recognition_vouchers")
  @@schema("crm")
}

// 申请认款附件表
model CrmPaymentRecognitionAttachment {
  // 主键
  id                  BigInt   @id /// 附件记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID

  // 附件信息
  fileName            String   @map("file_name") @db.VarChar(255) /// 文件名
  originalName        String   @map("original_name") @db.VarChar(255) /// 原始文件名
  fileUrl             String   @map("file_url") @db.Text /// 文件URL
  fileSize            BigInt   @map("file_size") /// 文件大小（字节）
  fileType            String   @map("file_type") @db.VarChar(100) /// 文件类型
  extension           String   @db.VarChar(10) /// 文件扩展名
  sortOrder           Int      @default(0) @map("sort_order") /// 排序顺序

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_attachment_application_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_attachment_deleted_at")
  @@map("payment_recognition_attachments")
  @@schema("crm")
}


// ../apps/finance/prisma/models/base.prisma
// Finance 财务管理相关模型

// 银行流水表
model BankJournal {
  id                        BigInt   @id // 雪花ID
  channelId                 BigInt   @map("channel_id") // 渠道ID，关联base.channel表
  type                      Int      @default(0) @map("type") // 类型：0-收入，1-支出
  tradeDate                 BigInt   @map("trade_date") // 支付日期（毫秒时间戳）
  transactionSerialNumber   String?  @map("transaction_serial_number") @db.VarChar(50) // 交易流水号
  paymentAmount             Decimal  @map("payment_amount") @db.Decimal(13,2) // 付款金额
  payerName                 String?  @map("payer_name") @db.VarChar(255) // 付款人姓名
  payerAccount              String?  @map("payer_account") @db.VarChar(255) // 付款人账号
  payerBank                 String?  @map("payer_bank") @db.VarChar(255) // 付款人开户行
  payeeOrderId              String?  @map("payee_order_id") @db.VarChar(255) // 收款单号
  payeeName                 String?  @map("payee_name") @db.VarChar(255) // 收款人名称
  payeeAccount              String?  @map("payee_account") @db.VarChar(255) // 收款人账号
  payeeBank                 String?  @map("payee_bank") @db.VarChar(255) // 收款人开户行
  remarks                   String?  @map("remarks") @db.VarChar(255) // 备注
  userId                    BigInt   @default(0) @map("user_id") // 添加者用户ID
  status                    Int      @default(0) @map("status") // 认款状态：0-待认款，1-已认款，2-认款申请中
  auditTime                 BigInt   @default(0) @map("audit_time") // 最终审核通过的时间
  attach                    String?  @map("attach") @db.VarChar(255) // 附件URL
  uploadRecordId            BigInt?  @map("upload_record_id") // 关联的导入记录ID

  // 系统字段 - 使用毫秒时间戳
  createdAt                 BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                 BigInt   @map("updated_at")
  deletedAt                 BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                 BigInt?  @map("created_by") // 创建人
  updatedBy                 BigInt?  @map("updated_by") // 更新人

  // 关联关系
  uploadRecord              BankJournalUploadRecord? @relation(fields: [uploadRecordId], references: [id])
  importDetails             BankJournalImportDetail[] @relation(name: "JournalImportDetails")

  @@map("bank_journals")
  @@schema("finance")
  @@index([payeeName], map: "idx_bank_journal_payee_name")
  @@index([payerName], map: "idx_bank_journal_payer_name")
  @@index([payeeOrderId], map: "idx_bank_journal_payee_order_id")
  @@index([tradeDate], map: "idx_bank_journal_trade_date")
  @@index([channelId], map: "idx_bank_journal_channel_id")
  @@index([status], map: "idx_bank_journal_status")
}

// 银行流水导入记录表
model BankJournalUploadRecord {
  id                        BigInt   @id // 雪花ID
  upFileName                String?  @map("up_file_name") @db.VarChar(255) // 导入文件名
  upFileUrl                 String?  @map("up_file_url") @db.VarChar(255) // 导入文件地址
  channelId                 BigInt   @map("channel_id") // 导入渠道ID，关联base.channel表
  message                   String?  @map("message") @db.Text // 处理消息结果
  downFileUrl               String?  @map("down_file_url") @db.VarChar(255) // 下载文件地址
  uploadTime                BigInt?  @map("upload_time") // 上传文件时间（毫秒时间戳）
  completionTime            BigInt?  @map("completion_time") // 处理完成时间（毫秒时间戳）
  status                    Int?     @map("status") // 状态：0-处理中，1-已完成，2-处理异常
  takeTime                  Int?     @map("take_time") // 耗时秒
  totalCount                Int      @default(0) @map("total_count") // 总记录数
  successCount              Int      @default(0) @map("success_count") // 成功导入数
  failCount                 Int      @default(0) @map("fail_count") // 失败数

  // 系统字段 - 使用毫秒时间戳
  createdAt                 BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                 BigInt   @map("updated_at")
  deletedAt                 BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                 BigInt?  @map("created_by") // 创建人
  updatedBy                 BigInt?  @map("updated_by") // 更新人

  // 关联关系
  journals                  BankJournal[]
  importDetails             BankJournalImportDetail[]

  @@map("bank_journal_upload_records")
  @@schema("finance")
  @@index([channelId], map: "idx_upload_record_channel_id")
  @@index([status], map: "idx_upload_record_status")
  @@index([createdAt], map: "idx_upload_record_created_at")
}

// 银行流水导入详细记录表
model BankJournalImportDetail {
  id                              BigInt   @id @default(autoincrement()) // 自增ID
  uploadRecordId                  BigInt   @map("upload_record_id") // 关联导入记录ID
  rowNumber                       Int      @map("row_number") // Excel行号

  // 流水数据字段（保持与bank_journals表一致的结构）
  tradeDate                       BigInt?  @map("trade_date") // 交易日期（毫秒时间戳）
  transactionSerialNumber         String?  @map("transaction_serial_number") @db.VarChar(50) // 交易流水号
  paymentAmount                   Decimal? @map("payment_amount") @db.Decimal(13,2) // 付款金额
  payerName                       String?  @map("payer_name") @db.VarChar(255) // 付款人姓名
  payerAccount                    String?  @map("payer_account") @db.VarChar(255) // 付款人账号
  payerBank                       String?  @map("payer_bank") @db.VarChar(255) // 付款人开户行
  payeeOrderId                    String?  @map("payee_order_id") @db.VarChar(100) // 收款订单号
  payeeName                       String?  @map("payee_name") @db.VarChar(255) // 收款人名称
  payeeAccount                    String?  @map("payee_account") @db.VarChar(255) // 收款人账号
  payeeBank                       String?  @map("payee_bank") @db.VarChar(255) // 收款人开户行
  remarks                         String?  @map("remarks") @db.Text // 备注

  // 导入状态和结果
  importStatus                    String   @map("import_status") @db.VarChar(20) // 导入状态：success, failed
  errorReason                     String?  @map("error_reason") @db.Text // 失败原因详细描述
  journalId                       BigInt?  @map("journal_id") // 成功导入后的流水记录ID

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  uploadRecord                    BankJournalUploadRecord @relation(fields: [uploadRecordId], references: [id])
  journal                         BankJournal? @relation(fields: [journalId], references: [id], name: "JournalImportDetails")

  @@map("bank_journal_import_details")
  @@schema("finance")
  @@index([uploadRecordId], map: "idx_bank_journal_import_details_upload_record_id")
  @@index([importStatus], map: "idx_bank_journal_import_details_import_status")
  @@index([uploadRecordId, rowNumber], map: "idx_bank_journal_import_details_row_number")
  @@index([journalId], map: "idx_bank_journal_import_details_journal_id")
  @@index([createdAt], map: "idx_bank_journal_import_details_created_at")
}

// 付款凭证表
model PaymentVoucher {
  id                              BigInt   @id // 雪花ID
  voucherNumber                   String   @unique @map("voucher_number") @db.VarChar(50) // 凭证编号，自动生成
  voucherType                     Int      @map("voucher_type") // 凭证类型：0-银行流水，1-现金收款，2-其他
  amount                          Decimal  @map("amount") @db.Decimal(13,2) // 金额
  payerName                       String   @map("payer_name") @db.VarChar(255) // 付款方名称
  payerBankAccount                String?  @map("payer_bank_account") @db.VarChar(100) // 付款方银行账号
  payerBankName                   String?  @map("payer_bank_name") @db.VarChar(200) // 付款方银行名称
  paymentDate                     BigInt   @map("payment_date") // 付款日期（毫秒时间戳）
  remarks                         String?  @map("remarks") @db.Text // 备注
  status                          Int      @default(0) @map("status") // 关联状态：0-未关联，1-已关联
  mergedStatus                    Int      @default(0) @map("merged_status") // 合并状态：0-未合并，1-已合并，2-合并源凭证
  mergedVoucherId                 BigInt?  @map("merged_voucher_id") // 合并后的凭证ID（如果是被合并的凭证）
  mergedReason                    String?  @map("merged_reason") @db.Text // 合并原因说明
  originalVoucherIds              String?  @map("original_voucher_ids") @db.Text // 原始凭证ID列表（JSON格式，用于合并后的凭证）

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  attachments                     PaymentVoucherAttachment[]

  @@map("payment_vouchers")
  @@schema("finance")
  @@index([voucherType], map: "idx_payment_voucher_type")
  @@index([status], map: "idx_payment_voucher_status")
  @@index([paymentDate], map: "idx_payment_voucher_payment_date")
  @@index([payerName], map: "idx_payment_voucher_payer_name")
  @@index([createdAt], map: "idx_payment_voucher_created_at")
}

// 付款凭证附件表
model PaymentVoucherAttachment {
  id                              BigInt   @id // 雪花ID
  voucherId                       BigInt   @map("voucher_id") // 关联付款凭证ID

  // 关联关系
  voucher                         PaymentVoucher @relation(fields: [voucherId], references: [id])

  @@map("payment_voucher_attachments")
  @@schema("finance")
  @@index([voucherId], map: "idx_payment_voucher_attachment_voucher_id")
}


// ../apps/mall/prisma/models/sensitive_words.prisma
// 敏感词表
model sensitive_words {
  id                    BigInt                @id @default(autoincrement()) // 敏感词ID，主键，自增长
  word                  String                @db.VarChar(100) // 敏感词内容
  word_type             Int                   @default(1) // 敏感词类型：1-一般敏感词，2-严重敏感词，3-政治敏感词
  replacement           String?               @db.VarChar(100) // 替换词，如果为空则用*号替换
  category              String?               @db.VarChar(50) // 敏感词分类：profanity-脏话，politics-政治，advertising-广告等
  severity_level        Int                   @default(1) // 严重程度：1-轻微，2-中等，3-严重
  action_type           Int                   @default(1) // 处理方式：1-替换，2-屏蔽，3-人工审核
  status                Int                   @default(1) // 状态：1-启用，0-禁用
  remark                String?               @db.Text // 备注说明
  
  // 审计字段
  created_by            BigInt?               // 创建人ID
  created_at            BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_by            BigInt?               // 更新人ID
  updated_at            BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  deleted_at            BigInt?               // 删除时间（时间戳，毫秒），用于软删除

  // 索引
  @@index([word], map: "idx_sensitive_words_word")
  @@index([word_type], map: "idx_sensitive_words_word_type")
  @@index([category], map: "idx_sensitive_words_category")
  @@index([status], map: "idx_sensitive_words_status")
  @@index([deleted_at], map: "idx_sensitive_words_deleted_at")

  @@map("sensitive_words")
  @@schema("base")
}


// ../apps/master/prisma/models/base_invoice_models.prisma
/// 统一发票抬头表，存储各端用户创建的发票抬头信息
/// 基于服务商表结构，去除provider关键词，增加source_type字段区分数据来源
model InvoiceHeader {
  // 主键
  id            BigInt    @id                           /// 发票抬头ID，16位雪花算法，系统自动生成
  
  // 关联字段
  user_id       BigInt                                  /// 用户ID，关联不同端的用户表
  source_type   Int       @default(0)                   /// 数据来源：0-服务商端，1-商家端，2-直销端
  
  // 发票抬头基本信息
  name          String    @db.VarChar(100)              /// 发票抬头名称，必填
  tax_number    String?   @db.VarChar(50)               /// 纳税人识别号
  phone         String?   @db.VarChar(20)               /// 公司电话
  address       String?   @db.VarChar(200)              /// 公司地址
  
  // 银行信息
  bank_name     String?   @db.VarChar(100)              /// 开户银行名称
  bank_account  String?   @db.VarChar(30)               /// 银行账号
  
  // 状态信息
  is_default    Boolean   @default(false)               /// 是否为默认抬头
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  applications  InvoiceApplication[]                    /// 关联的开票申请
  
  // 索引
  @@index([user_id], name: "idx_invoice_headers_user_id")
  @@index([source_type], name: "idx_invoice_headers_source_type")
  @@index([name], name: "idx_invoice_headers_name")
  @@index([is_default], name: "idx_invoice_headers_is_default")
  @@index([status], name: "idx_invoice_headers_status")
  @@index([deleted_at], name: "idx_invoice_headers_deleted_at")
  
  @@map("invoice_headers")
  @@schema("base")
}

/// 统一开票申请记录表，存储各端开票申请的基本信息
/// 基于服务商表结构，包含完整的审核流程和开票信息
model InvoiceApplication {
  // 主键
  id                    BigInt    @id                           /// 申请ID，16位雪花算法，系统自动生成
  
  // 关联字段
  user_id               BigInt                                  /// 申请人ID，关联不同端的用户表
  source_type           Int       @default(0)                   /// 数据来源：0-服务商端，1-商家端，2-直销端
  order_id              BigInt                                  /// 关联订单ID，关联base.orders表
  invoice_header_id     BigInt?                                 /// 发票抬头ID，关联invoice_headers表（可为空）
  
  // 申请基本信息
  application_no        String    @unique @db.VarChar(50)       /// 申请单号，系统自动生成，唯一
  invoice_type          String    @db.VarChar(20)               /// 发票类型：电子普票、电子专票、纸质普票、纸质专票
  total_amount          Decimal   @db.Decimal(15,2)             /// 申请开票总金额
  tax_amount            Decimal   @db.Decimal(15,2)             /// 税额
  untaxed_amount        Decimal   @db.Decimal(15,2)             /// 未税金额
  
  // 审核状态
  status                Int       @default(1)                   /// 申请状态：1-待商务审核，2-商务审核通过，3-商务审核驳回，4-待财务审核，5-财务审核通过，6-财务审核驳回，7-已开票，8-已红冲
  business_auditor_id   BigInt?                                 /// 商务审核人ID，关联base.system_user表
  business_audit_time   BigInt?                                 /// 商务审核时间戳（毫秒）
  business_audit_remark String?   @db.Text                      /// 商务审核备注
  finance_auditor_id    BigInt?                                 /// 财务审核人ID，关联base.system_user表
  finance_audit_time    BigInt?                                 /// 财务审核时间戳（毫秒）
  finance_audit_remark  String?   @db.Text                      /// 财务审核备注
  
  // 开票信息
  invoice_number        String?   @db.VarChar(50)               /// 发票号码
  invoice_code          String?   @db.VarChar(50)               /// 发票代码
  invoice_date          BigInt?                                 /// 开票日期时间戳（毫秒）
  invoice_file_url      String?   @db.VarChar(500)              /// 发票文件下载地址
  invoicer_id           BigInt?                                 /// 开票人ID，关联base.system_user表
  
  // 红冲相关
  is_red_invoice        Boolean   @default(false)               /// 是否为红冲发票
  original_application_id BigInt?                               /// 原申请ID（红冲时关联原申请）
  red_invoice_reason    String?   @db.Text                      /// 红冲原因
  
  // 申请备注
  remark                String?   @db.Text                      /// 申请备注
  
  // 审计字段
  created_by            BigInt?                                 /// 创建者ID，16位
  updated_by            BigInt?                                 /// 更新者ID，16位
  created_at            BigInt                                  /// 创建时间戳（毫秒）
  updated_at            BigInt                                  /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  header                InvoiceHeader?                          @relation(fields: [invoice_header_id], references: [id])
  details               InvoiceApplicationDetail[]              /// 申请详情
  
  // 索引
  @@index([user_id], name: "idx_invoice_applications_user_id")
  @@index([source_type], name: "idx_invoice_applications_source_type")
  @@index([order_id], name: "idx_invoice_applications_order_id")
  @@index([invoice_header_id], name: "idx_invoice_applications_header_id")
  @@index([application_no], name: "idx_invoice_applications_no")
  @@index([status], name: "idx_invoice_applications_status")
  @@index([business_auditor_id], name: "idx_invoice_applications_business_auditor")
  @@index([finance_auditor_id], name: "idx_invoice_applications_finance_auditor")
  @@index([invoice_number], name: "idx_invoice_applications_invoice_number")
  @@index([is_red_invoice], name: "idx_invoice_applications_is_red")
  @@index([original_application_id], name: "idx_invoice_applications_original_id")
  @@index([created_at], name: "idx_invoice_applications_created_at")
  @@index([deleted_at], name: "idx_invoice_applications_deleted_at")
  
  @@map("invoice_applications")
  @@schema("base")
}

/// 统一开票申请详情表，存储各端开票申请的商品明细信息
/// 包含商品信息、价格计算、税收分类等完整字段
model InvoiceApplicationDetail {
  // 主键
  id                    BigInt    @id                           /// 详情ID，16位雪花算法，系统自动生成
  
  // 关联字段
  application_id        BigInt                                  /// 申请ID，关联invoice_applications表
  
  // 商品信息
  product_name          String    @db.VarChar(200)              /// 商品名称
  product_model         String?   @db.VarChar(100)              /// 规格型号
  unit                  String    @db.VarChar(20)               /// 单位
  quantity              Decimal   @db.Decimal(15,4)             /// 数量
  
  // 价格信息
  unit_price            Decimal   @db.Decimal(15,4)             /// 未税单价
  untaxed_amount        Decimal   @db.Decimal(15,2)             /// 未税金额
  tax_rate              Decimal   @db.Decimal(5,2)              /// 税率（%）
  tax_amount            Decimal   @db.Decimal(15,2)             /// 税额
  taxed_amount          Decimal   @db.Decimal(15,2)             /// 含税金额
  
  // 税收分类
  tax_classification_code String  @db.VarChar(50)               /// 税收分类编码
  tax_classification_name String  @db.VarChar(200)              /// 税收分类名称
  
  // 订单相关信息
  order_detail_id       BigInt?                                 /// 关联的订单详情ID
  sku_code              String?   @db.VarChar(100)              /// SKU编码
  
  // 审计字段
  created_by            BigInt?                                 /// 创建者ID，16位
  updated_by            BigInt?                                 /// 更新者ID，16位
  created_at            BigInt                                  /// 创建时间戳（毫秒）
  updated_at            BigInt                                  /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  application           InvoiceApplication                      @relation(fields: [application_id], references: [id])
  
  // 索引
  @@index([application_id], name: "idx_invoice_application_details_app_id")
  @@index([order_detail_id], name: "idx_invoice_application_details_order_detail_id")
  @@index([tax_classification_code], name: "idx_invoice_application_details_tax_code")
  @@index([sku_code], name: "idx_invoice_application_details_sku_code")
  @@index([created_at], name: "idx_invoice_application_details_created_at")
  @@index([deleted_at], name: "idx_invoice_application_details_deleted_at")
  
  @@map("invoice_application_details")
  @@schema("base")
}


// ../apps/master/prisma/models/base.prisma





// ../apps/master/prisma/models/csm/after_sales_type_template.prisma
/// 售后类型内容模板表，存储售后申请表单中的售后类型和对应的模板内容
model CsmAfterSalesTypeTemplate {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 售后类型模板ID，自增主键
  
  // 基本信息
  pid           BigInt    @default(0)                          /// 父级ID，0表示顶级分类
  name          String    @db.VarChar(100)                     /// 售后类型名称，必填，最大100字符
  template      String?   @db.Text                             /// 模板内容，选填（父级分类可能没有模板）
  sort_order    Int       @default(0)                          /// 排序字段，默认为0
  status        Int       @default(1)                          /// 状态：1-启用，0-禁用，默认启用
  
  // 审计字段
  created_at    BigInt                                         /// 创建时间戳（毫秒）
  updated_at    BigInt                                         /// 更新时间戳（毫秒）
  created_by    BigInt?                                        /// 创建人ID，选填
  updated_by    BigInt?                                        /// 更新人ID，选填
  deleted_at    BigInt?                                        /// 删除时间戳（毫秒）（软删除）

  // 自关联关系（支持层级结构）
  parent        CsmAfterSalesTypeTemplate?  @relation("ParentChild", fields: [pid], references: [id])
  children      CsmAfterSalesTypeTemplate[] @relation("ParentChild")

  // 索引
  @@index([pid])                                               /// 父级ID索引，用于查询子分类
  @@index([status])                                            /// 状态索引，用于过滤启用/禁用的记录
  @@index([deleted_at])                                        /// 软删除索引
  @@index([created_at])                                        /// 创建时间索引
  @@index([sort_order])                                        /// 排序索引

  @@map("csm_after_sales_type_template")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/afterSalesApplication.prisma
// 售后申请主表
model afterSalesApplication {
  id                        BigInt    @id @default(autoincrement())
  after_sales_number        String    @unique @db.VarChar(64) // 售后编号

  // 关联订单信息
  original_order_id         BigInt? // 原始订单ID
  original_order_number     String?   @db.VarChar(64) // 原始订单号
  purchase_order_id         BigInt? // 采购订单ID
  purchase_order_number     String?   @db.VarChar(64) // 采购订单号

  // 售后基础信息
  after_sales_type          String    @db.VarChar(50) // 售后类型：退货、换货、维修、退款
  after_sales_content       String    @db.Text // 售后内容描述
  customer_demand           String    @db.Text // 客户诉求

  // 人员信息
  applicant_id              BigInt? // 申请人ID
  applicant_name            String?   @db.VarChar(100) // 申请人姓名
  purchaser_id              BigInt? // 采购员ID
  purchaser_name            String?   @db.VarChar(100) // 采购员姓名
  after_sales_staff_id      BigInt? // 售后员ID
  after_sales_staff_name    String?   @db.VarChar(100) // 售后员姓名
  follower_name             String?   @db.VarChar(100) // 跟单员
  sales_staff_name          String?   @db.VarChar(100) // 销售员
  sales_department          String?   @db.VarChar(100) // 销售部门

  // 订单基础信息（从采购订单同步）
  order_source              String?   @db.VarChar(50) // 订单来源
  order_type                String?   @db.VarChar(100) // 订单类型
  buyer_account             String?   @db.VarChar(200) // 买家账号
  delivery_address          String?   @db.Text // 收货地址

  // 供应商信息
  supplier_id               BigInt? // 供应商ID
  supplier_name             String?   @db.VarChar(200) // 实际供应商
  is_own_brand              Boolean   @default(false) // 是否自主品牌

  // 售后状态
  after_sales_status        Int       @default(0) // 售后状态：0-待处理 1-处理中 2-已完成 3-已关闭
  after_sales_progress      Int?      @default(0) // 售后进度：0-跟进补发单号 1-跟进退货 2-跟进退款 3-跟进换货 4-跟进维修结果 5-待客户回复 6-待供应商回复 7-补发维修中 8-理赔中 9-跟进安装结果 10-跟进物流派送

  // 退货信息
  business_return           Boolean   @default(false) // 业务是否退货
  customer_service_return   Boolean   @default(false) // 客服是否退货
  return_address            String?   @db.Text // 退货地址
  return_number             String?   @db.VarChar(100) // 退货单号

  // 回复信息
  reply_content             String?   @db.Text // 回复内容
  reply_time                DateTime? // 回复时间
  replier_id                BigInt? // 回复人ID
  replier_name              String?   @db.VarChar(100) // 回复人姓名

  // 责任归属
  responsibility            String?   @db.VarChar(100) // 责任归属

  // ERP系统状态
  erp_status                Int?      @default(0) // ERP操作状态：0-全部 1-完成erp系统操作 2-已操作采购退料 3-已下推费用单 4-其他

  // 备注信息
  remark                    String?   @db.Text // 备注
  
  // 系统字段
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt
  deleted_at                DateTime?
  
  // 关联关系
  purchase_order            purchase_order? @relation(fields: [purchase_order_id], references: [id])
  product_items             after_sales_product_item[]
  attachments               after_sales_attachment[]
  reply_history             after_sales_reply_history[]
  logs                      after_sales_log[]
  
  @@map("after_sales_application")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/afterSalesAttachment.prisma
// 售后附件表
model after_sales_attachment {
  id                        BigInt    @id @default(autoincrement())
  after_sales_id            BigInt // 售后申请ID
  
  // 附件信息
  attachment_type           String    @db.VarChar(50) // 附件类型：application-申请附件, reply-回复附件, remark-备注附件
  file_name                 String    @db.VarChar(255) // 文件名
  file_url                  String    @db.Text // 文件URL
  file_size                 BigInt? // 文件大小（字节）
  file_type                 String?   @db.VarChar(100) // 文件类型
  
  // 上传信息
  uploader_id               BigInt? // 上传人ID
  uploader_name             String?   @db.VarChar(100) // 上传人姓名
  
  // 系统字段
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt
  deleted_at                DateTime?
  
  // 关联关系
  after_sales_application   afterSalesApplication @relation(fields: [after_sales_id], references: [id])
  
  @@map("after_sales_attachment")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/afterSalesLog.prisma
// 售后操作日志表
model after_sales_log {
  id                        BigInt    @id @default(autoincrement())
  after_sales_id            BigInt // 售后申请ID
  
  // 操作信息
  operation_type            String    @db.VarChar(50) // 操作类型：create, update_status, reply, remark, change_staff等
  operation_content         String?   @db.Text // 操作内容描述
  old_value                 String?   @db.Text // 原值
  new_value                 String?   @db.Text // 新值
  
  // 操作人信息
  operator_id               BigInt? // 操作人ID
  operator_name             String?   @db.VarChar(100) // 操作人姓名
  operator_role             String?   @db.VarChar(50) // 操作人角色
  
  // 系统字段
  created_at                DateTime  @default(now())
  
  // 关联关系
  after_sales_application   afterSalesApplication @relation(fields: [after_sales_id], references: [id])
  
  @@map("after_sales_log")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/afterSalesProductItem.prisma
// 售后商品项表
model after_sales_product_item {
  id             BigInt @id @default(autoincrement())
  after_sales_id BigInt // 售后申请ID

  // 商品信息（从采购订单商品项同步）
  purchase_order_item_id BigInt? // 采购订单商品项ID
  goods_sku_id           BigInt? // 商品SKU ID
  goods_spu_id           BigInt? // 商品ID
  product_name           String  @db.Text // 商品名称
  product_code           String? @db.VarChar(100) // 商品编码
  sku_code               String? @db.VarChar(100) // SKU编码
  specification          String? @db.Text // 规格
  product_image          String? @db.Text // 商品图片

  // 价格和数量信息
  unit_price Decimal? @db.Decimal(12, 2) // 单价
  sale_price Decimal? @db.Decimal(12, 2) // 销售价
  cost_price Decimal? @db.Decimal(12, 2) // 成本价
  quantity   Int // 数量

  // 退货状态
  is_return       Boolean @default(false) // 是否退货
  return_quantity Int     @default(0) // 退货数量

  // 供应商信息
  suggested_supplier_id   BigInt? // 建议供应商ID
  suggested_supplier_name String? @db.VarChar(200) // 建议供应商名称
  actual_supplier_id      BigInt? // 实际供应商ID
  actual_supplier_name    String? @db.VarChar(200) // 实际供应商名称

  // 物流信息
  shipping_status Int?    @default(0) // 物流状态 (0=待发货,1=已发货,2=已收货)
  shipping_number String? @db.VarChar(100) // 物流单号
  shipping_info   String? @db.Text // 物流信息

  // 系统字段
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?

  // 关联关系
  after_sales_application afterSalesApplication @relation(fields: [after_sales_id], references: [id])

  @@map("after_sales_product_item")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/afterSalesReplyHistory.prisma
// 售后回复历史表
model after_sales_reply_history {
  id                        BigInt    @id @default(autoincrement())
  after_sales_id            BigInt // 售后申请ID

  // 回复信息
  reply_content             String    @db.Text // 回复内容
  reply_type                String?   @db.VarChar(50) // 回复类型：customer-客户回复, staff-员工回复

  // 回复人信息
  replier_id                BigInt? // 回复人ID
  replier_name              String?   @db.VarChar(100) // 回复人姓名
  replier_role              String?   @db.VarChar(50) // 回复人角色

  // 责任归属信息
  responsibility            Int? // 责任归属：0-客户问题 1-供应商问题 2-物流问题 3-销售员问题 4-采购问题 5-产品问题

  // 费用信息
  company_cost_type         Int? // 我司售后费用类型：0-快递费 1-安装费 2-其他费用
  company_cost_amount       Decimal? @db.Decimal(12, 2) // 我司承担费用金额
  supplier_cost_type        Int? // 供应商售后费用类型：0-快递费 1-安装费 2-其他费用
  supplier_cost_amount      Decimal? @db.Decimal(12, 2) // 供应商承担费用金额
  total_cost_amount         Decimal? @db.Decimal(12, 2) // 总费用金额

  // 退货信息
  is_return                 Boolean? @default(false) // 是否退货

  // 附件信息
  reply_attachments         String? @db.Text // 回复附件JSON字符串

  // 系统字段
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt
  deleted_at                DateTime?

  // 关联关系
  after_sales_application   afterSalesApplication @relation(fields: [after_sales_id], references: [id])

  @@map("after_sales_reply_history")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/cooperation_agreement.prisma
/// 合作协议表，存储供应商基本信息管理中的合作协议数据
model CsmCooperationAgreement {
  // 主键
  id              BigInt    @id @default(autoincrement())      /// 合作协议ID，自增主键
  
  // 基本信息
  agreement_name  String    @db.VarChar(200)                  /// 合作协议名称，必填，最大200字符
  submitter       String    @db.VarChar(100)                  /// 提交人，必填，最大100字符
  submit_date     DateTime  @db.Date                          /// 提交日期，必填
  remark          String?   @db.Text                          /// 备注信息，选填
  updater         String    @db.VarChar(100)                  /// 更新人，必填，最大100字符
  
  // 审计字段
  created_at      BigInt                                      /// 创建时间戳（毫秒）
  updated_at      BigInt                                      /// 更新时间戳（毫秒）
  deleted_at      BigInt?                                     /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([deleted_at])
  @@index([agreement_name])
  @@index([submitter])
  
  @@map("csm_cooperation_agreements")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/delivery_method.prisma
/// 配送方式配置表，支持按渠道区分
model csm_delivery_method {
  id            Int       @id @default(autoincrement()) /// 自增主键，唯一标识一种配送方式
  method_name   String    @db.VarChar(100) /// 配送方式名称（如：标准快递、次日达）
  method_code   String    @db.VarChar(50) /// 配送方式编码（如：EXPRESS、NEXT_DAY）
  description   String?   @db.Text /// 配送方式描述信息，如时效说明
  channel_id    BigInt? /// 渠道ID（关联已有渠道表，NULL表示通用配送方式）
  is_enabled    Int       @default(1) /// 启用状态（INT类型）：1=启用，0=禁用
  create_time   DateTime  @default(now()) @db.Timestamp(6) /// 创建时间，自动生成
  update_time   DateTime? @db.Timestamp(6) /// 更新时间，手动维护

  // 关联关系
  channel       channel?  @relation(fields: [channel_id], references: [id], onDelete: SetNull)
  shipping_info csm_shipping_info[] /// 关联的发货信息

  // 索引和约束
  @@unique([method_code, channel_id], map: "uniq_method_code_channel")
  @@index([channel_id, is_enabled], map: "idx_delivery_channel_enabled")
  @@index([is_enabled], map: "idx_delivery_method_enabled")
  @@index([method_code], map: "idx_delivery_method_code")

  @@map("csm_delivery_method")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/enterprise_nature.prisma
/// 企业性质表，存储供应商基本信息管理中的企业性质数据
model CsmEnterpriseNature {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 企业性质ID，自增主键
  
  // 基本信息
  nature_name   String    @db.VarChar(200)                    /// 企业性质名称，必填，最大200字符
  submitter     String    @db.VarChar(100)                    /// 提交人，必填，最大100字符
  submit_date   DateTime  @db.Date                            /// 提交日期，必填
  remark        String?   @db.Text                            /// 备注信息，选填
  updater       String    @db.VarChar(100)                    /// 更新人，必填，最大100字符
  
  // 审计字段
  created_at    BigInt                                        /// 创建时间戳（毫秒）
  updated_at    BigInt                                        /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([deleted_at])
  @@index([nature_name])
  @@index([submitter])
  
  @@map("csm_enterprise_nature")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/inquiry_details.prisma
/// 询价详情表，存储询价单的具体产品信息
model csm_inquiry_details {
  // 主键
  id                    BigInt    @id @default(autoincrement()) /// 询价详情ID，主键，自增长
  
  // 关联信息
  inquiry_id            BigInt    /// 询价单ID
  inquiry_no            String    @db.VarChar(50) /// 询价序号
  
  // 产品信息
  product_name          String    @db.VarChar(255) /// 产品名称
  brand                 String?   @db.VarChar(100) /// 品牌
  model                 String?   @db.VarChar(100) /// 型号
  specification         String?   @db.Text /// 规格描述
  reference_link        String?   @db.VarChar(500) /// 参考链接
  unit                  String?   @db.VarChar(50) /// 单位
  quantity              Decimal   @db.Decimal(15,4) /// 需求数量
  reference_price       Decimal?  @db.Decimal(15,4) /// 参考价格
  customer_product_code String?   @db.VarChar(100) /// 客户产品编码
  inquiry_remark        String?   @db.Text /// 询价备注
  
  // 报价信息
  quote_price           Decimal?  @db.Decimal(15,4) /// 报价价格
  quote_status          Int       @default(0) /// 报价状态：0-未报价，1-已报价，2-已确认，3-已拒绝
  inquiry_status        Int       @default(0) /// 询价状态：0-未询价，1-已询价待审核，2-审核通过，3-审核驳回，4-已取消，5-已确认销售价，6-重新询价，7-资料待审核，8-资料审核驳回，9-资料审核通过，10-已操作上架，11-上游-上架状态
  
  // 时间戳
  created_at            BigInt    /// 创建时间（时间戳）
  updated_at            BigInt    /// 更新时间（时间戳）
  deleted_at            BigInt?   /// 删除时间（软删除）
  created_by            BigInt?   /// 创建人 ID
  updated_by            BigInt?   /// 更新人 ID
  
  // 关联关系
  inquiry_order         csm_inquiry_orders @relation(fields: [inquiry_id], references: [id], onDelete: Cascade)
  
  // 索引
  @@index([deleted_at])
  @@index([inquiry_id])
  @@index([inquiry_no])
  @@index([product_name])
  @@index([brand])
  @@index([quote_status])
  @@index([created_by])
  @@index([updated_by])
  
  @@map("csm_inquiry_details")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/inquiry_file_uploads.prisma
/// 询价文件上传记录表，存储用户上传的询价文件信息
model inquiry_file_uploads {
  // 主键
  id                BigInt    @id @default(autoincrement()) /// 文件上传记录ID，主键，自增长
  
  // 文件基本信息
  file_name         String    @db.VarChar(255) /// 文件名称
  file_url          String    @db.VarChar(500) /// 文件访问URL
  file_size         BigInt    @default(0) /// 文件大小（字节）
  file_type         String?   @db.VarChar(100) /// 文件MIME类型
  file_extension    String?   @db.VarChar(10) /// 文件扩展名
  
  // 处理状态
  upload_status     Int       @default(1) /// 上传状态：0-上传失败，1-上传成功
  parse_status      Int       @default(0) /// 解析状态：0-未解析，1-解析成功，2-解析失败
  import_status     Int       @default(0) /// 导入状态：0-未导入，1-导入成功，2-导入失败
  
  // 处理结果
  total_rows        Int?      @default(0) /// 总行数
  success_rows      Int?      @default(0) /// 成功行数
  failed_rows       Int?      @default(0) /// 失败行数
  error_message     String?   @db.Text /// 错误信息
  result_file_url   String?   @db.VarChar(500) /// 结果文件URL
  
  // 上传人信息
  uploaded_by       BigInt? /// 上传人ID
  
  // 审计字段
  created_at        BigInt /// 创建时间戳（毫秒）
  updated_at        BigInt /// 更新时间戳（毫秒）
  deleted_at        BigInt? /// 删除时间戳（毫秒），软删除
  
  // 索引
  @@index([uploaded_by], map: "idx_inquiry_file_uploads_uploaded_by")
  @@index([upload_status], map: "idx_inquiry_file_uploads_upload_status")
  @@index([parse_status], map: "idx_inquiry_file_uploads_parse_status")
  @@index([import_status], map: "idx_inquiry_file_uploads_import_status")
  @@index([created_at], map: "idx_inquiry_file_uploads_created_at")
  @@index([deleted_at], map: "idx_inquiry_file_uploads_deleted_at")
  
  // 表名映射
  @@map("inquiry_file_uploads")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/inquiry_orders.prisma
/// 询价单表，存储询价单基本信息
model csm_inquiry_orders {
  // 主键
  id                BigInt    @id @default(autoincrement()) /// 询价单ID，主键，自增长
  
  // 基本信息
  inquiry_no        String    @unique @db.VarChar(50) /// 询价单号，唯一
  customer_id       String    @db.VarChar(255) /// 客户ID
  contact_id        BigInt?   /// 联系人ID
  customer_address  String?   @db.Text /// 客户地址
  salesman_id       BigInt?   /// 业务员ID
  inquirer_id       BigInt?   /// 询价员ID
  channel_id        BigInt?   /// 下单渠道ID
  attachments       Json?     /// 附件信息（JSON格式存储）
  
  // 状态信息
  total_items       Int       @default(0) /// 询价项目总数
  remark            String?   @db.Text /// 备注
  
  // 时间戳
  created_at        BigInt    /// 创建时间（时间戳）
  updated_at        BigInt    /// 更新时间（时间戳）
  created_by        BigInt?   /// 创建人ID
  updated_by        BigInt?   /// 更新人ID
  deleted_at        BigInt?   /// 删除时间（软删除）
  
  // 关联关系
  inquiry_details   csm_inquiry_details[] /// 询价详情列表
  
  // 索引
  @@index([deleted_at])
  @@index([inquiry_no])
  @@index([customer_id])
  @@index([salesman_id])
  @@index([inquirer_id])
  @@index([channel_id])
  @@index([created_at])
  
  @@map("csm_inquiry_orders")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/inquiry_records.prisma
/// 询价记录表，存储单个商品的询价报价记录
model csm_inquiry_records {
  // 主键
  id                    BigInt    @id @default(autoincrement()) /// 询价记录ID，主键，自增长
  
  // 商品信息
  product_name          String    @db.VarChar(255) /// 商品名称
  product_code          String?   @db.VarChar(100) /// 商品编码/SKU
  product_image         String?   @db.VarChar(500) /// 商品图片URL
  specification         String?   @db.Text /// 规格描述
  brand                 String?   @db.VarChar(100) /// 品牌
  unit                  String?   @db.VarChar(50) /// 单位
  
  // 报价信息
  current_price         Decimal   @db.Decimal(15,4) /// 当前报价
  include_tax           Boolean   @default(false) /// 是否含税：true-含税，false-不含税
  include_shipping      Boolean   @default(false) /// 是否含运费：true-含运费，false-不含运费
  
  // 询价信息
  inquirer              String    @db.VarChar(100) /// 询价员姓名
  inquirer_id           BigInt?   /// 询价员ID
  inquiry_time          BigInt    /// 询价时间（时间戳）
  
  // 供应商信息
  supplier_id           BigInt?   /// 供应商ID
  supplier_name         String    @db.VarChar(255) /// 供应商名称
  supplier_code         String?   @db.VarChar(100) /// 供应商编码
  contact_person        String?   @db.VarChar(100) /// 联系人
  contact_position      String?   @db.VarChar(100) /// 联系人职位
  contact_phone         String?   @db.VarChar(50) /// 联系电话
  cooperation_status    String?   @db.VarChar(50) /// 合作状态
  
  // 状态信息
  record_status         Int       @default(0) /// 记录状态：0-有效，1-已失效，2-已删除
  
  // 备注信息
  remark                String?   @db.Text /// 备注
  
  // 时间戳
  created_at            BigInt    /// 创建时间（时间戳）
  updated_at            BigInt    /// 更新时间（时间戳）
  created_by            BigInt?   /// 创建人ID
  updated_by            BigInt?   /// 更新人ID
  deleted_at            BigInt?   /// 删除时间（软删除）
  
  // 索引
  @@index([deleted_at])
  @@index([product_name])
  @@index([product_code])
  @@index([brand])
  @@index([supplier_id])
  @@index([supplier_name])
  @@index([inquirer_id])
  @@index([inquiry_time])
  @@index([record_status])
  @@index([created_at])
  
  @@map("csm_inquiry_records")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/main_product.prisma
/// 主营产品表，存储供应商基本信息管理中的主营产品数据
model CsmMainProduct {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 主营产品ID，自增主键
  
  // 基本信息
  product_name  String    @db.VarChar(200)                    /// 主营产品名称，必填，最大200字符
  submitter     String    @db.VarChar(100)                    /// 提交人，必填，最大100字符
  submit_date   DateTime  @db.Date                            /// 提交日期，必填
  remark        String?   @db.Text                            /// 备注信息，选填
  updater       String    @db.VarChar(100)                    /// 更新人，必填，最大100字符
  
  // 审计字段
  created_at    BigInt                                        /// 创建时间戳（毫秒）
  updated_at    BigInt                                        /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  supplier_products CsmSupplierMainProduct[]                  /// 供应商主营产品关联

  // 索引
  @@index([deleted_at])
  @@index([product_name])
  @@index([submitter])

  @@map("csm_main_products")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/payment_term.prisma
/// 付款条件表，存储供应商基本信息管理中的付款条件数据
model CsmPaymentTerm {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 付款条件ID，自增主键
  
  // 基本信息
  term_name     String    @db.VarChar(200)                    /// 付款条件名称，必填，最大200字符
  submitter     String    @db.VarChar(100)                    /// 提交人，必填，最大100字符
  submit_date   DateTime  @db.Date                            /// 提交日期，必填
  remark        String?   @db.Text                            /// 备注信息，选填
  updater       String    @db.VarChar(100)                    /// 更新人，必填，最大100字符
  
  // 审计字段
  created_at    BigInt                                        /// 创建时间戳（毫秒）
  updated_at    BigInt                                        /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([deleted_at])
  @@index([term_name])
  @@index([submitter])
  
  @@map("csm_payment_terms")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_attachment.prisma
// 采购订单附件表
model purchase_order_attachment {
  id                        BigInt                   @id @default(autoincrement()) // 附件ID，主键，自增长
  purchase_order_id         BigInt                   // 采购订单ID
  
  // 附件信息
  file_name                 String                   @db.VarChar(255) // 文件名
  file_path                 String                   @db.VarChar(500) // 文件路径
  file_url                  String?                  @db.VarChar(500) // 文件URL
  file_size                 BigInt? // 文件大小（字节）
  file_type                 String?                  @db.VarChar(100) // 文件类型
  mime_type                 String?                  @db.VarChar(100) // MIME类型
  
  // 附件分类
  attachment_type           String?                  @db.VarChar(50) // 附件类型：contract-合同, invoice-发票, other-其他
  attachment_category       String?                  @db.VarChar(100) // 附件分类
  
  // 描述信息
  description               String?                  @db.Text // 附件描述
  remark                    String?                  @db.Text // 备注
  
  // 上传信息
  uploader                  String?                  @db.VarChar(100) // 上传人
  upload_time               DateTime                 @default(now()) // 上传时间
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id])
  
  @@index([purchase_order_id], map: "idx_purchase_order_attachment_order_id")
  @@index([attachment_type], map: "idx_purchase_order_attachment_type")
  @@index([upload_time], map: "idx_purchase_order_attachment_upload_time")
  @@index([created_at], map: "idx_purchase_order_attachment_created_at")
  @@index([deleted_at], map: "idx_purchase_order_attachment_deleted_at")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_expense_item.prisma
// 采购订单费用单商品明细表
model purchase_order_expense_item {
  id                        BigInt                   @id @default(autoincrement()) // 明细ID，主键，自增长
  expense_id                BigInt                   // 关联费用单ID
  purchase_order_item_id    BigInt                   // 关联采购订单商品项ID
  
  // 商品信息（冗余字段，便于查询和历史记录）
  product_name              String                   @db.Text // 商品名称
  product_code              String?                  @db.VarChar(100) // 商品编码
  sku                       String?                  @db.VarChar(100) // SKU
  specification             String?                  @db.Text // 规格型号
  quantity                  Int                      // 商品数量
  
  // 价格信息（冗余字段）
  purchase_unit_price       Decimal?                 @db.Decimal(12, 2) // 采购单价
  purchase_total_amount     Decimal?                 @db.Decimal(12, 2) // 采购合价
  sales_unit_price          Decimal?                 @db.Decimal(12, 2) // 销售单价
  sales_total_amount        Decimal?                 @db.Decimal(12, 2) // 销售合价
  
  // 时间信息
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  
  // 关联关系
  expense                   purchase_order_expense   @relation(fields: [expense_id], references: [id])
  purchase_order_item       purchase_order_item      @relation(fields: [purchase_order_item_id], references: [id])
  
  @@index([expense_id], map: "idx_expense_item_expense_id")
  @@index([purchase_order_item_id], map: "idx_expense_item_purchase_order_item_id")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_expense.prisma
// 采购订单费用单主表
model purchase_order_expense {
  id                        BigInt                   @id @default(autoincrement()) // 费用单ID，主键，自增长
  expense_number            String                   @unique @db.VarChar(64) // 费用单编号，唯一
  purchase_order_id         BigInt                   // 关联采购订单ID
  purchase_order_number     String?                  @db.VarChar(64) // 采购订单编号（冗余字段）
  
  // 费用信息
  expense_type              String                   @db.VarChar(50) // 费用类型代码
  expense_type_name         String?                  @db.VarChar(100) // 费用类型名称
  expense_amount            Decimal                  @db.Decimal(12, 2) // 费用金额
  
  // 订单信息（冗余字段，便于费用单查看）
  follower                  String?                  @db.VarChar(100) // 跟单员
  purchaser                 String?                  @db.VarChar(100) // 采购员
  sales_total_amount        Decimal?                 @db.Decimal(12, 2) // 销售总价（选中商品的销售合价总和）
  purchase_total_amount     Decimal?                 @db.Decimal(12, 2) // 采购总价（选中商品的采购合价总和）
  
  // 备注信息
  remark                    String?                  @db.Text // 备注
  
  // 操作人员信息
  creator_id                BigInt? // 创建人ID
  creator_name              String?                  @db.VarChar(100) // 创建人姓名
  updater_id                BigInt? // 最后更新人ID
  updater_name              String?                  @db.VarChar(100) // 最后更新人姓名
  
  // 时间信息
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id])
  expense_items             purchase_order_expense_item[] // 费用单商品明细
  
  @@index([purchase_order_id], map: "idx_expense_purchase_order_id")
  @@index([expense_number], map: "idx_expense_number")
  @@index([expense_type], map: "idx_expense_type")
  @@index([created_at], map: "idx_expense_created_at")
  @@index([creator_id], map: "idx_expense_creator_id")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_item.prisma
// 采购订单商品项表
model purchase_order_item {
  id                        BigInt                   @id @default(autoincrement()) // 商品项ID，主键，自增长
  purchase_order_id         BigInt                   // 采购订单ID
  original_order_item_id    BigInt?                  // 原始订单商品项ID
  
  // 商品信息
  goods_spu_id              BigInt? // 商品SPU ID
  goods_sku_id              BigInt                   // 商品SKU ID
  spu_code_snapshot         String?                  @db.VarChar(100) // SPU编码快照
  spu_name_snapshot         String?                  @db.Text // SPU名称快照
  product_name              String                   @db.Text // 商品名称
  product_code              String?                  @db.VarChar(100) // 商品编码
  sku_code                  String?                  @db.VarChar(100) // SKU编码
  sku                       String?                  @db.VarChar(100) // SKU
  sku_specifications        Json? // SKU规格信息
  specification             String?                  @db.Text // 规格
  product_image             String?                  @db.Text // 商品图片
  
  // 价格信息
  unit_price                Decimal                  @db.Decimal(12, 2) // 单价
  market_price_snapshot     Decimal?                 @db.Decimal(12, 2) // 市场价快照
  cost_price_snapshot       Decimal?                 @db.Decimal(12, 2) // 成本价快照
  purchase_price            Decimal?                 @db.Decimal(12, 2) // 采购价格
  supply_price              Decimal?                 @db.Decimal(12, 2) // 供应价格
  contract_price            Decimal?                 @db.Decimal(12, 2) // 合同单价

  // 成本信息
  actual_cost               Decimal?                 @db.Decimal(12, 2) // 实际成本
  inspection_cost           Decimal?                 @db.Decimal(12, 2) // 验货成本
  profit_rate               String?                  @db.VarChar(20) // 利润率
  cost_total                Decimal?                 @db.Decimal(12, 2) // 商品成本总价
  
  // 数量信息
  quantity                  Int                      @default(1) // 数量
  purchased_quantity        Int                      @default(0) // 已采购数量
  received_quantity         Int                      @default(0) // 已收货数量
  split_quantity            Int                      @default(0) // 已拆分数量
  available_split_quantity  Int                      @default(0) // 可拆分数量
  
  // 金额信息
  total_price               Decimal                  @db.Decimal(12, 2) // 总价
  total_amount              Decimal?                 @db.Decimal(12, 2) // 总金额
  item_paid_amount          Decimal                  @db.Decimal(12, 2) // 商品项已支付金额
  purchase_total            Decimal?                 @db.Decimal(12, 2) // 采购总额
  
  // 第三方信息
  third_party_spu_id        String?                  @db.VarChar(255) // 第三方SPU ID
  third_party_sku_id        String?                  @db.VarChar(255) // 第三方SKU ID
  third_party_product_code  String?                  @db.VarChar(255) // 第三方商品编码
  third_party_item_snapshot Json? // 第三方商品信息快照
  
  // 供应商信息
  suggested_supplier_id     BigInt? // 建议供应商ID
  suggested_supplier_name   String?                  @db.VarChar(200) // 建议供应商名称
  actual_supplier_id        BigInt? // 实际供应商ID
  actual_supplier_name      String?                  @db.VarChar(200) // 实际供应商名称

  // 税收分类
  tax_category              String?                  @db.VarChar(100) // 税收分类
  
  // 物流信息
  weight_snapshot           Decimal?                 @db.Decimal(10, 3) // 重量快照
  volume_snapshot           Decimal?                 @db.Decimal(10, 6) // 体积快照
  shipping_info             String?                  @db.Text // 物流信息
  
  // 状态信息
  item_status               Int                      @default(0) // 商品项状态：0-待处理 1-已分配 2-采购中 3-已完成 4-已取消
  purchase_progress         String?                  @db.VarChar(50) // 采购进度
  
  // 时间信息
  expected_delivery_time    DateTime? // 预期交货时间
  actual_delivery_time      DateTime? // 实际交货时间
  expiry_time               DateTime? // 过期时间
  product_line_expire_time  DateTime? // 产品线过期时间
  
  // 备注信息
  item_remark               String?                  @db.Text // 商品项备注
  loss_reason               String?                  @db.VarChar(100) // 亏损原因：freight_loss/packaging_loss等
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id])
  split_items               purchase_order_split_item[] // 拆分商品项
  expense_items             purchase_order_expense_item[] // 费用单商品明细（反向关联）
  
  @@index([purchase_order_id], map: "idx_purchase_order_item_order_id")
  @@index([goods_sku_id], map: "idx_purchase_order_item_sku_id")
  @@index([suggested_supplier_id], map: "idx_purchase_order_item_suggested_supplier")
  @@index([actual_supplier_id], map: "idx_purchase_order_item_actual_supplier")
  @@index([item_status], map: "idx_purchase_order_item_status")
  @@index([created_at], map: "idx_purchase_order_item_created_at")
  @@index([deleted_at], map: "idx_purchase_order_item_deleted_at")
  @@index([product_code], map: "idx_purchase_order_item_product_code")
  @@index([sku], map: "idx_purchase_order_item_sku")
  @@index([tax_category], map: "idx_purchase_order_item_tax_category")
  @@index([product_line_expire_time], map: "idx_purchase_order_item_product_line_expire")
  @@index([loss_reason], map: "idx_purchase_order_item_loss_reason")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_log.prisma
// 采购订单日志表
model purchase_order_log {
  id                        BigInt                   @id @default(autoincrement()) // 日志ID，主键，自增长
  purchase_order_id         BigInt                   // 采购订单ID
  
  // 日志信息
  action_type               String                   @db.VarChar(50) // 操作类型：create-创建, assign-分配, update-更新, complete-完成, cancel-取消
  action_description        String?                  @db.Text // 操作描述
  
  // 状态变更
  old_status                Int? // 原状态
  new_status                Int? // 新状态
  status_description        String?                  @db.VarChar(200) // 状态描述
  
  // 操作人信息
  operator_id               BigInt? // 操作人ID
  operator_name             String?                  @db.VarChar(100) // 操作人姓名
  operator_role             String?                  @db.VarChar(50) // 操作人角色
  
  // 详细信息
  details                   Json? // 详细信息（JSON格式）
  remark                    String?                  @db.Text // 备注
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id])
  
  @@index([purchase_order_id], map: "idx_purchase_order_log_order_id")
  @@index([action_type], map: "idx_purchase_order_log_action_type")
  @@index([operator_id], map: "idx_purchase_order_log_operator_id")
  @@index([created_at], map: "idx_purchase_order_log_created_at")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_shipping_link.prisma
// 采购订单发货链接表
model purchase_order_shipping_link {
  id                        BigInt                   @id @default(autoincrement()) // 发货链接ID，主键，自增长
  
  // 链接信息
  shipping_link             String                   @db.VarChar(500) // 发货链接
  qr_code_url               String?                  @db.VarChar(500) // 发货二维码地址
  
  // 订单关联信息
  original_order_number     String                   @db.VarChar(64) // 原订单号
  purchase_order_id         BigInt                   // 采购订单ID
  purchase_order_number     String                   @db.VarChar(64) // 采购订单号
  split_order_id            BigInt?                  // 拆分订单ID（可为空）
  split_order_number        String?                  @db.VarChar(64) // 拆分订单号（可为空）
  
  // 收货信息
  recipient_name            String                   @db.VarChar(100) // 收货人
  recipient_phone           String                   @db.VarChar(20) // 收货人电话
  recipient_address         String                   @db.Text // 收货地址
  
  // 发货类型：1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流
  shipping_type             Int                      @default(1) // 发货类型

  // 发货状态：0-未发货 1-已发货
  shipping_completed        Int                      @default(0) // 发货完成状态

  // 操作人信息
  operator_id               BigInt                   // 操作人ID
  operator_name             String                   @db.VarChar(100) // 操作人姓名
  
  // 时间信息
  generated_at              DateTime                 @default(now()) // 生成时间
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  purchase_order            purchase_order           @relation(fields: [purchase_order_id], references: [id], map: "fk_shipping_link_purchase_order_id") // 关联采购订单（一对一）
  split_order               purchase_order_split?    @relation(fields: [split_order_id], references: [id], map: "fk_shipping_link_split_order_id") // 关联拆分订单（一对一，可选）
  
  @@unique([purchase_order_id], map: "uk_shipping_link_purchase_order_id")
  @@unique([split_order_id], map: "uk_shipping_link_split_order_id")
  @@index([original_order_number], map: "idx_shipping_link_original_order_number")
  @@index([purchase_order_number], map: "idx_shipping_link_purchase_order_number")
  @@index([split_order_number], map: "idx_shipping_link_split_order_number")
  @@index([operator_id], map: "idx_shipping_link_operator_id")
  @@index([shipping_type], map: "idx_shipping_link_shipping_type")
  @@index([shipping_completed], map: "idx_shipping_link_shipping_completed")
  @@index([generated_at], map: "idx_shipping_link_generated_at")
  @@index([created_at], map: "idx_shipping_link_created_at")
  @@index([deleted_at], map: "idx_shipping_link_deleted_at")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_split_item.prisma
// 采购订单拆分商品项表
model purchase_order_split_item {
  id                        BigInt                   @id @default(autoincrement()) // 拆分商品项ID，主键，自增长
  split_id                  BigInt                   // 拆分单ID
  original_item_id          BigInt                   // 原始商品项ID
  
  // 拆分配置信息（通过拆分单号区分，不需要组索引和名称）
  
  // 商品信息快照（从原始商品项复制）
  goods_spu_id              BigInt?                  // 商品SPU ID
  goods_sku_id              BigInt                   // 商品SKU ID
  product_name              String                   @db.Text // 商品名称
  product_code              String?                  @db.VarChar(100) // 商品编码
  sku_code                  String?                  @db.VarChar(100) // SKU编码
  sku                       String?                  @db.VarChar(100) // SKU
  specification             String?                  @db.Text // 规格
  product_image             String?                  @db.Text // 商品图片
  
  // 价格信息快照
  unit_price                Decimal                  @db.Decimal(12, 2) // 单价

  // 数量信息
  split_quantity            Int                      @default(0) // 本次拆分数量
  
  // 状态信息（独立跟踪）
  shipping_status           Int                      @default(0) // 发货状态：0-待发货 1-发货未完成 2-待收货 3-已收货
  shipping_number           String?                  @db.VarChar(100) // 物流单号
  shipping_info             String?                  @db.Text // 物流信息
  
  // 系统字段
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime?                // 删除时间
  
  // 关联关系
  split_order               purchase_order_split     @relation(fields: [split_id], references: [id])
  original_item             purchase_order_item      @relation(fields: [original_item_id], references: [id])
  
  @@index([split_id], map: "idx_split_item_split_id")
  @@index([original_item_id], map: "idx_split_item_original_item_id")
  @@index([goods_sku_id], map: "idx_split_item_sku_id")
  @@index([shipping_status], map: "idx_split_item_shipping_status")
  @@index([created_at], map: "idx_split_item_created_at")
  @@index([deleted_at], map: "idx_split_item_deleted_at")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order_split.prisma
// 采购订单拆分单主表
model purchase_order_split {
  id                        BigInt                   @id @default(autoincrement()) // 拆分单ID，主键，自增长
  split_number              String                   @unique @db.VarChar(64) // 拆分单编号，唯一
  original_purchase_order_id BigInt                  // 原始采购订单ID
  original_order_number     String                   @db.VarChar(64) // 原始采购订单编号
  
  // 拆分基础信息
  split_type                Int                      @default(1) // 拆分类型：1-单商品拆分 2-多商品拆分

  // 收货信息（从原订单复制）
  recipient_name            String?                  @db.VarChar(100) // 收货人姓名
  recipient_phone           String?                  @db.VarChar(20) // 收货电话
  delivery_address          String?                  @db.Text // 配送地址
  order_source              String?                  @db.VarChar(50) // 订单来源
  
  // 拆分状态
  split_status              Int                      @default(0) // 拆分状态：0-待处理 1-拆分中 2-已完成 3-已取消

  // 数量统计
  total_split_quantity      Int                      @default(0) // 拆分总数量
  
  // 操作人员信息
  splitter_id               BigInt?                  // 拆分操作员ID
  splitter_name             String?                  @db.VarChar(100) // 拆分操作员姓名

  // 时间信息
  split_time                DateTime                 @default(now()) // 拆分时间
  
  // 系统字段
  submitter                 String?                  @db.VarChar(100) // 提交人
  updater                   String?                  @db.VarChar(100) // 更新人
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime?                // 删除时间
  
  // 关联关系
  original_purchase_order   purchase_order           @relation(fields: [original_purchase_order_id], references: [id])
  split_items               purchase_order_split_item[] // 拆分商品项
  shipping_link             purchase_order_shipping_link? // 发货链接
  
  @@index([split_number], map: "idx_split_number")
  @@index([original_purchase_order_id], map: "idx_original_purchase_order_id")
  @@index([split_status], map: "idx_split_status")
  @@index([splitter_id], map: "idx_splitter_id")
  @@index([split_time], map: "idx_split_time")
  @@index([created_at], map: "idx_split_created_at")
  @@index([deleted_at], map: "idx_split_deleted_at")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/purchase_order.prisma
// 采购订单主表
model purchase_order {
  id                        BigInt                   @id @default(autoincrement()) // 采购订单ID，主键，自增长
  purchase_order_number     String                   @unique @db.VarChar(64) // 采购订单编号，唯一
  original_order_id         BigInt                   // 原始订单ID，关联base.orders表
  original_order_number     String?                  @db.VarChar(64) // 原始订单编号
  third_party_order_sn      String?                  @db.VarChar(128) // 第三方订单号
  
  // 采购相关信息
  purchaser_id              BigInt?                  // 采购员ID
  purchaser_name            String?                  @db.VarChar(100) // 采购员姓名
  supplier_id               BigInt?                  // 供应商ID
  supplier_name             String?                  @db.VarChar(200) // 供应商名称
  supplier_code             String?                  @db.VarChar(100) // 供应商编码

  // 订单基础信息
  order_source              String?                  @db.VarChar(50) // 订单来源：商城/竞价/手动
  order_type                String?                  @db.VarChar(100) // 订单类型：商城自然单/竞价自然单等
  follower                  String?                  @db.VarChar(100) // 跟单员
  buyer_account             String?                  @db.VarChar(200) // 下单账号
  delivery_address          String?                  @db.Text // 配送地址

  // 订单状态
  purchase_status           Int                      @default(0) // 采购状态：0-待处理 1-已分配 2-采购中 3-已完成 4-已取消
  order_status              Int                      @default(0) // 订单状态：0-待发货 1-发货未完成 2-待收货 3-已废止 4-已关闭 5-废止待确认
  payment_status            Int                      @default(0) // 支付状态：同步自原订单
  shipping_status           Int                      @default(0) // 配送状态：同步自原订单

  // 扩展状态信息
  erp_status                Int                      @default(0) // ERP状态：0-待同步 1-已同步 2-同步失败
  split_order_status        Int                      @default(0) // 拆单状态：0-未拆分 1-已拆分 2-拆分中
  split_status              Int                      @default(0) // 商品拆分状态：0-未拆分 1-部分拆分 2-全部拆分 3-拆分中
  audit_status              Int                      @default(0) // 审核状态：0-待审核 1-审核通过 2-审核驳回 3-废止待确认
  cancel_status             Int?                     // 取消状态：0-待废止 1-废止待确认 2-已废止
  pending_cancel_status     Int?                     // 待取消状态：0-待废止 1-废止待确认 2-废止审核中 3-采购员待审核
  link_status               Int                      @default(0) // 链接状态：0-unlinked 1-linked
  is_loss                   Int                      @default(0) // 是否亏损：0-no 1-yes
  loss_reason               String?                  @db.VarChar(100) // 亏损原因：freight_loss/quote_error等
  link_generated            Boolean                  @default(false) // 是否生成链接
  
  // 金额信息
  total_product_amount      Decimal                  @default(0.00) @db.Decimal(12, 2) // 商品总金额
  shipping_fee              Decimal                  @default(0.00) @db.Decimal(10, 2) // 运费
  freight                   Decimal                  @default(0.00) @db.Decimal(10, 2) // 运费(页面字段)
  discount_amount           Decimal                  @default(0.00) @db.Decimal(10, 2) // 折扣金额
  tax_amount                Decimal                  @default(0.00) @db.Decimal(10, 2) // 税费
  total_amount              Decimal                  @default(0.00) @db.Decimal(12, 2) // 订单总金额
  total_quantity            Int                      @default(0) // 总数量
  split_count               Int                      @default(0) // 拆分次数
  purchase_cost             Decimal?                 @db.Decimal(12, 2) // 采购成本
  cost_total                Decimal?                 @db.Decimal(12, 2) // 成本总价
  gross_profit_rate         String?                  @db.VarChar(20) // 毛利率

  // 物流信息
  logistics_info            String?                  @db.VarChar(200) // 物流信息
  logistics_number          String?                  @db.VarChar(100) // 物流单号
  
  // 收货信息
  recipient_name            String?                  @db.VarChar(100) // 收货人姓名
  recipient_phone           String?                  @db.VarChar(20) // 收货人电话
  recipient_address         String?                  @db.Text // 收货地址
  actual_receiver           String?                  @db.VarChar(100) // 实际收货人
  actual_phone              String?                  @db.VarChar(20) // 实际联系电话
  actual_address            String?                  @db.Text // 实际收货地址

  // 备注信息
  business_remark           String?                  @db.Text // 业务员备注
  order_remark              String?                  @db.Text // 订单备注
  customer_remark           String?                  @db.Text // 客户备注
  purchase_remark           String?                  @db.Text // 采购备注
  
  // 渠道信息
  channel_id                BigInt? // 渠道ID
  channel_name              String?                  @db.VarChar(100) // 渠道名称
  platform_id               BigInt? // 平台ID
  platform_name             String?                  @db.VarChar(100) // 平台名称
  store_id                  BigInt? // 店铺ID
  store_name                String?                  @db.VarChar(100) // 店铺名称
  
  // 时间信息
  original_order_time       DateTime? // 原始下单时间
  purchase_time             DateTime? // 采购时间
  purchase_apply_time       DateTime                 @default(now()) // 申请采购时间
  purchase_assign_time      DateTime? // 分配采购员时间
  purchase_complete_time    DateTime? // 采购完成时间
  expected_delivery_time    DateTime? // 预期交货时间
  actual_delivery_time      DateTime? // 实际交货时间
  
  // 系统字段
  submitter                 String?                  @db.VarChar(100) // 提交人
  updater                   String?                  @db.VarChar(100) // 更新人
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  original_order            orders                @relation(fields: [original_order_id], references: [id], map: "fk_purchase_order_original_order_id") // 关联原订单
  purchase_order_items      purchase_order_item[] // 采购订单商品项
  purchase_order_attachments purchase_order_attachment[] // 采购订单附件
  purchase_order_logs       purchase_order_log[] // 采购订单日志
  purchase_order_splits     purchase_order_split[] // 采购订单拆分单
  purchase_order_expenses   purchase_order_expense[] // 采购订单费用单
  after_sales_applications  afterSalesApplication[] // 售后申请
  purchase_order_shipping_link purchase_order_shipping_link? // 采购订单发货链接
  
  @@index([purchase_order_number], map: "idx_purchase_order_number")
  @@index([original_order_id], map: "idx_original_order_id")
  @@index([purchaser_id], map: "idx_purchaser_id")
  @@index([supplier_id], map: "idx_supplier_id")
  @@index([purchase_status], map: "idx_purchase_status")
  @@index([purchase_apply_time], map: "idx_purchase_apply_time")
  @@index([created_at], map: "idx_purchase_order_created_at")
  @@index([deleted_at], map: "idx_purchase_order_deleted_at")
  @@index([order_source], map: "idx_purchase_order_order_source")
  @@index([order_type], map: "idx_purchase_order_order_type")
  @@index([follower], map: "idx_purchase_order_follower")
  @@index([erp_status], map: "idx_purchase_order_erp_status")
  @@index([split_status], map: "idx_purchase_order_split_status")
  @@index([audit_status], map: "idx_purchase_order_audit_status")
  @@index([link_status], map: "idx_purchase_order_link_status")
  @@index([is_loss], map: "idx_purchase_order_is_loss")
  @@index([logistics_number], map: "idx_purchase_order_logistics_number")
  @@index([purchase_time], map: "idx_purchase_order_purchase_time")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/shipping_info.prisma
/// 发货信息主表，记录订单物流配送详情
model csm_shipping_info {
  id                    BigInt                @id @default(autoincrement()) /// 自增主键，唯一标识一条发货记录
  order_no              String                @db.VarChar(50) /// 订单编号，关联业务系统订单标识
  order_type            Int /// 订单类型（INT类型）：1=采购订单，2=拆分商品
  link_id               BigInt? /// 发货链接ID，关联purchase_order_shipping_link表
  delivery_method_id    Int /// 配送方式ID，关联csm_delivery_method表主键
  express_company_name  String                @db.VarChar(100) /// 物流公司名称（如：顺丰速运）
  express_company_id    String                @db.VarChar(50) /// 物流公司编码（如：SF）
  tracking_no           String                @db.VarChar(50) /// 物流单号，用于跟踪物流状态
  shipping_location     String                @db.VarChar(200) /// 发货地址，详细到门牌号
  attachment            String?               @db.VarChar(500) /// 附件路径，多个附件用逗号分隔
  delivery_list_photo   String?               @db.VarChar(500) /// 配送清单照片路径，支持多张
  package_photo         String?               @db.VarChar(500) /// 包裹照片路径，支持多张
  business_contact      String                @db.VarChar(50) /// 业务联系人姓名
  business_phone        String                @db.VarChar(20) /// 业务联系电话，支持手机/固话
  remarks               String?               @db.Text /// 发货备注信息，如特殊要求
  create_time           DateTime              @default(now()) @db.Timestamp(6) /// 记录创建时间，自动生成
  update_time           DateTime?             @db.Timestamp(6) /// 记录更新时间，手动维护
  delete_time           DateTime?             @db.Timestamp(6) /// 逻辑删除标记（NULL=未删除，非NULL=删除时间）

  // 关联关系
  delivery_method       csm_delivery_method   @relation(fields: [delivery_method_id], references: [id])

  // 索引
  @@index([order_no, order_type], map: "idx_shipping_order")
  @@index([tracking_no], map: "idx_tracking_no")
  @@index([create_time], map: "idx_shipping_create_time")
  @@index([delete_time], map: "idx_shipping_delete_time")
  @@index([link_id], map: "idx_shipping_link_id")

  @@map("csm_shipping_info")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_agreement.prisma
/// 供应商协议表
model CsmSupplierAgreement {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 协议ID，自增主键

  // 关联信息
  supplier_id           BigInt                                        /// 供应商ID
  supplier              CsmSupplier @relation(fields: [supplier_id], references: [id], onDelete: Cascade)

  // 协议信息
  agreement_type        String    @db.VarChar(100)                    /// 协议类型：供货协议、保密协议等
  agreement_name        String    @db.VarChar(200)                    /// 协议名称，必填
  start_date            DateTime? @db.Date                            /// 协议开始日期
  end_date              DateTime? @db.Date                            /// 协议结束日期
  duration              String?   @db.VarChar(100)                    /// 协议期限
  status                String    @db.VarChar(50)                     /// 协议状态：active、expired、terminated等
  sign_date             DateTime? @db.Date                            /// 签署日期
  file_url              String?   @db.VarChar(500)                    /// 协议文件URL
  remark                String?   @db.Text                            /// 备注信息

  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([supplier_id])
  @@index([agreement_type])
  @@index([status])
  @@index([deleted_at])

  @@map("csm_supplier_agreements")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_attachment.prisma
/// 供应商附件表
model CsmSupplierAttachment {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 附件ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 供应商ID
  supplier              CsmSupplier @relation(fields: [supplier_id], references: [id], onDelete: Cascade)
  
  // 附件信息
  attachment_type       String    @db.VarChar(50)                     /// 附件类型：business_license、bank_proof、id_card、others等
  file_name             String    @db.VarChar(200)                    /// 文件名称，必填
  file_url              String    @db.VarChar(500)                    /// 文件URL，必填
  file_size             BigInt?                                       /// 文件大小（字节）
  file_type             String?   @db.VarChar(100)                    /// 文件类型
  sub_type              String?   @db.VarChar(50)                     /// 子类型：如身份证的front、back
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([supplier_id])
  @@index([attachment_type])
  @@index([deleted_at])
  
  @@map("csm_supplier_attachments")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_brand.prisma
/// 供应商品牌授权表
model CsmSupplierBrand {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 品牌授权ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 供应商ID
  supplier              CsmSupplier @relation(fields: [supplier_id], references: [id], onDelete: Cascade)
  
  // 品牌信息
  brand_name            String    @db.VarChar(100)                    /// 品牌名称，必填
  authorized            Boolean   @default(false)                     /// 是否已授权
  authorization_date    DateTime? @db.Date                            /// 授权日期
  expiry_date           DateTime? @db.Date                            /// 授权到期日期
  authorization_scope   String?   @db.VarChar(500)                    /// 授权范围
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([supplier_id])
  @@index([brand_name])
  @@index([deleted_at])
  
  @@map("csm_supplier_brands")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_contact.prisma
/// 供应商联系人表
model CsmSupplierContact {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 联系人ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 供应商ID
  supplier              CsmSupplier @relation(fields: [supplier_id], references: [id], onDelete: Cascade)
  
  // 联系人信息
  name                  String    @db.VarChar(100)                    /// 联系人姓名，必填
  phone                 String    @db.VarChar(50)                     /// 联系电话，必填
  position              String?   @db.VarChar(100)                    /// 职位
  email                 String?   @db.VarChar(200)                    /// 邮箱
  department            String?   @db.VarChar(100)                    /// 部门
  is_primary            Boolean   @default(false)                     /// 是否主要联系人
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([supplier_id])
  @@index([name])
  @@index([deleted_at])
  
  @@map("csm_supplier_contacts")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_main_product.prisma
/// 供应商主营产品关联表
model CsmSupplierMainProduct {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 关联ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 供应商ID
  supplier              CsmSupplier @relation(fields: [supplier_id], references: [id], onDelete: Cascade)
  main_product_id       BigInt                                        /// 主营产品ID
  main_product          CsmMainProduct @relation(fields: [main_product_id], references: [id])
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([supplier_id])
  @@index([main_product_id])
  @@index([deleted_at])
  
  // 联合唯一索引，防止重复关联
  @@unique([supplier_id, main_product_id])
  
  @@map("csm_supplier_main_products")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier_relation.prisma
/// 供应商关联表，存储供应商之间的关联关系（多对多）
model CsmSupplierRelation {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 关联ID，自增主键
  
  // 关联信息
  supplier_id           BigInt                                        /// 主供应商ID
  related_supplier_id   BigInt                                        /// 关联供应商ID
  relation_type         String    @db.VarChar(50)                     /// 关联类型：独立供应商、关联供应商、子公司、分公司、战略合作伙伴等
  
  // 关联详情
  relation_desc         String?   @db.VarChar(500)                    /// 关联描述
  start_date            DateTime? @db.Date                            /// 关联开始日期
  end_date              DateTime? @db.Date                            /// 关联结束日期
  status                String    @default("active") @db.VarChar(20)  /// 关联状态：active=有效、inactive=无效、suspended=暂停
  
  // 操作信息
  creator               String    @db.VarChar(100)                    /// 创建人
  updater               String?   @db.VarChar(100)                    /// 更新人
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  supplier              CsmSupplier @relation("SupplierRelations", fields: [supplier_id], references: [id])
  related_supplier      CsmSupplier @relation("RelatedSupplierRelations", fields: [related_supplier_id], references: [id])

  // 索引
  @@index([supplier_id])
  @@index([related_supplier_id])
  @@index([relation_type])
  @@index([status])
  @@index([deleted_at])
  
  // 联合唯一索引，防止重复关联
  @@unique([supplier_id, related_supplier_id])
  
  @@map("csm_supplier_relations")
  @@schema("csm")
}


// ../apps/master/prisma/models/csm/supplier.prisma
/// 供应商主表，存储供应商基本信息
model CsmSupplier {
  // 主键
  id                    BigInt    @id @default(autoincrement())        /// 供应商ID，自增主键
  
  // 基础信息
  supplier_name         String    @db.VarChar(200)                    /// 供应商名称，必填
  supplier_code         String    @unique @db.VarChar(50)             /// 供应商代码，必填，唯一
  contact_person        String    @db.VarChar(100)                    /// 主要联系人，必填
  position              String?   @db.VarChar(100)                    /// 联系人职位
  phone                 String    @db.VarChar(50)                     /// 联系电话，必填
  company_nature        String    @db.VarChar(100)                    /// 公司性质
  address               String    @db.VarChar(500)                    /// 通信地址
  detailed_address      String?   @db.VarChar(500)                    /// 详细地址
  production_address    String?   @db.VarChar(500)                    /// 生产地址
  
  // 合作信息
  cooperation_type      String    @db.VarChar(50)                     /// 合作方式：授权、代加工、代加工+授权、独家代理等
  cooperation_status    String    @db.VarChar(50)                     /// 合作状态：启用、待启用、黑名单、不启用等
  payment_terms         String    @db.VarChar(200)                    /// 付款条件
  supplier_relation     String?   @db.VarChar(100)                    /// 供应商关联：独立供应商、战略合作伙伴、授权代理商等
  supplier_group        String?   @db.VarChar(50)                     /// 供应商分组：A类供应商、B类供应商等
  
  // 企业信息
  credit_code           String?   @db.VarChar(50)                     /// 统一社会信用代码
  registered_capital    Decimal?  @db.Decimal(15,2)                   /// 注册资本（万元）
  register_time         DateTime? @db.Date                            /// 注册时间
  cooperation_agreement String?   @db.VarChar(200)                    /// 合作协议名称
  order_count          Int?      @default(0)                         /// 订单数量
  
  // 账户信息
  invoice_type          String?   @db.VarChar(50)                     /// 发票类型
  tax_rate              Decimal?  @db.Decimal(5,2)                    /// 税率
  account_name          String?   @db.VarChar(200)                    /// 账户名称
  settlement_method     String?   @db.VarChar(50)                     /// 结算方式
  account_number        String?   @db.VarChar(100)                    /// 账户号码
  bank_name             String?   @db.VarChar(200)                    /// 开户银行
  
  // 操作信息
  department            String    @db.VarChar(100)                    /// 部门
  submitter             String    @db.VarChar(100)                    /// 提交人，必填
  submit_date           DateTime  @db.Date                            /// 提交日期，必填
  updater               String?   @db.VarChar(100)                    /// 更新人
  reviewer              String?   @db.VarChar(100)                    /// 复审人
  remark                String?   @db.Text                            /// 备注信息
  
  // 审计字段
  created_at            BigInt                                        /// 创建时间戳（毫秒）
  updated_at            BigInt                                        /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                       /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  brands                CsmSupplierBrand[]                            /// 品牌授权信息
  contacts              CsmSupplierContact[]                          /// 联系人信息
  agreements            CsmSupplierAgreement[]                        /// 协议信息
  attachments           CsmSupplierAttachment[]                       /// 附件信息
  main_products         CsmSupplierMainProduct[]                      /// 主营产品关联
  supplier_relations    CsmSupplierRelation[] @relation("SupplierRelations")        /// 作为主供应商的关联关系
  related_supplier_relations CsmSupplierRelation[] @relation("RelatedSupplierRelations") /// 作为关联供应商的关联关系

  // 索引
  @@index([deleted_at])
  @@index([supplier_name])
  @@index([supplier_code])
  @@index([submitter])
  @@index([cooperation_status])
  @@index([cooperation_type])
  
  @@map("csm_suppliers")
  @@schema("csm")
}


// ../apps/master/prisma/models/express/express_company_code.prisma
// 快递公司编码模型
model ExpressCompanyCode {
  id          BigInt   @id
  company_name String
  company_code String
  company_type String
  created_at   Int
  updated_at   Int
  deleted_at   Int?

  // 反向关联关系
  platform_express_mappings PlatformExpressMapping[] @relation("PlatformExpressMapping_express_company")

  @@map("express_company_code")
  @@schema("base")
}


// ../apps/master/prisma/models/express/express_platform_mapping.prisma
// 平台快递编码映射模型
model PlatformExpressMapping {
  id                    BigInt   @id
  express_id            BigInt                                  // 系统标准快递编码ID，关联express_company_code表
  express_code          String   @db.VarChar(50)                     // 系统标准快递编码编，关联express_company_code表
  express_name          String   @db.VarChar(50)                     // 系统标准快递编码名称，关联express_company_code表
  platform_id           BigInt                                  // 平台ID，关联platform表
  platform_express_code String   @db.VarChar(50)               // 平台快递编码
  platform_express_name String?  @db.VarChar(100)              // 平台快递名称
  mapping_type          String   @default("auto") @db.VarChar(20) // 映射类型：auto自动，manual手动
  is_active             Int      @default(1)                    // 是否启用：1启用，0禁用
  priority              Int      @default(0)                    // 优先级，数字越大优先级越高
  confidence_score      Decimal  @default(0.00) @db.Decimal(3,2) // 匹配置信度(0.00-1.00)
  created_at            BigInt   @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  updated_at            BigInt   @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  created_by            BigInt?
  updated_by            BigInt?
  deleted_at            BigInt?

  // 关联关系
  express_company       ExpressCompanyCode @relation("PlatformExpressMapping_express_company", fields: [express_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  // 索引
  @@unique([platform_id, platform_express_code], name: "idx_platform_express_unique")
  @@index([platform_id], name: "idx_platform_express_mapping_platform")
  @@index([express_id], name: "idx_platform_express_mapping_system_id")
  @@index([is_active], name: "idx_platform_express_mapping_active")
  @@index([mapping_type], name: "idx_platform_express_mapping_type")
  @@index([priority], name: "idx_platform_express_mapping_priority")
  
  @@map("express_platform_mapping")
  @@schema("base")
}


// ../apps/master/prisma/models/express/express_subscription.prisma
// 快递订阅记录表
model expressSubscription {
  id                  BigInt  @id @default(autoincrement())
  package_id          BigInt  // 包裹ID，关联order_packages表
  order_id            BigInt  // 订单ID
  express_no          String  @db.VarChar(100) // 快递单号
  express_code        String  @db.VarChar(50)  // 快递公司编码
  subscription_status String  @default("pending") @db.VarChar(20) // 订阅状态：pending待订阅，subscribed已订阅，failed订阅失败
  subscription_time   BigInt? // 订阅时间戳
  callback_count      Int     @default(0) // 回调次数
  last_callback_time  BigInt? // 最后回调时间
  error_message       String? @db.Text // 错误信息
  created_at          BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  updated_at          BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  deleted_at          BigInt?

  // 关联关系
  package             order_packages @relation(fields: [package_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([package_id, express_no])
  @@index([package_id])
  @@index([subscription_status])
  @@index([express_no])
  @@map("express_subscription")
  @@schema("base")
}


// ../apps/master/prisma/models/express/express_track.prisma
// 快递轨迹表
model expressTrack {
  id               BigInt  @id @default(autoincrement())
  package_id       BigInt  // 包裹ID，关联order_packages表
  order_id         BigInt  // 订单ID
  express_no       String  @db.VarChar(100) // 快递单号
  express_code     String  @db.VarChar(50)  // 快递公司编码
  express_name     String? @db.VarChar(100) // 快递公司名称
  status           String  @default("0") @db.VarChar(10) // 物流状态：0在途，1揽收，2疑难，3签收，4退签，5派件，6退回
  condition_code   String? @db.VarChar(10) // 快递100状态码
  is_check         String  @default("0") @db.VarChar(1) // 是否签收：0未签收，1已签收
  track_data       Json?   // 完整轨迹数据
  last_update_time BigInt? // 最后更新时间戳
  created_at       BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  updated_at       BigInt  @default(dbgenerated("(EXTRACT(epoch FROM now()) * 1000)"))
  deleted_at       BigInt?

  @@unique([package_id, express_no])
  @@index([package_id])
  @@index([order_id])
  @@index([express_no])
  @@map("express_track")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_associations.prisma
model GoodsAssociation {
  id                                                            BigInt     @id @default(autoincrement()) // 主键ID
  source_goods_spu_id                                           BigInt     // 源商品SPU ID
  target_goods_spu_id                                           BigInt     // 目标商品SPU ID
  association_type                                              String     @db.VarChar(50) // 预留字段 关联类型，如"相关商品"、"搭配商品"等
  created_at                                                    BigInt     // 创建时间戳（毫秒）
  updated_at                                                    BigInt     // 更新时间戳（毫秒）
  // 暂时注释掉与GoodsSpu的关系，以便解决循环引用问题
  // goods_spus_goods_associations_source_goods_spu_idTogoods_spus GoodsSpu  @relation("goods_associations_source_goods_spu_idTogoods_spus", fields: [source_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  // goods_spus_goods_associations_target_goods_spu_idTogoods_spus GoodsSpu  @relation("goods_associations_target_goods_spu_idTogoods_spus", fields: [target_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([source_goods_spu_id], map: "idx_goods_associations_source_goods_spu_id")
  @@index([target_goods_spu_id], map: "idx_goods_associations_target_goods_spu_id")
  @@index([association_type], map: "idx_goods_associations_type")
  @@map("goods_associations")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_attribute_items.prisma
model GoodsAttributeItem {
  id                     BigInt                   @id // 主键ID
  goods_attribute_set_id BigInt                    // 属性集ID
  name                   String                   @db.VarChar(100) // 属性名称
  type                   String                   @db.VarChar(50) // 属性类型（如文本、数字、选项等）
  value                  Json?                    // 属性值（JSON格式）
  is_required            Int                      @default(0) // 是否必填：0-否，1-是
  is_filterable          Int                      @default(0) // 是否可筛选：0-否，1-是
  sort_order             Int                      @default(0) // 排序顺序
  is_enabled             Int                      @default(1) // 是否启用：0-禁用，1-启用
  created_at             BigInt                   @default(0) // 创建时间戳（毫秒）
  updated_at             BigInt                   @default(0) // 更新时间戳（毫秒）
  deleted_at             BigInt?                  // 删除时间戳（毫秒，软删除）
  created_by             BigInt?                  // 创建人ID
  updated_by             BigInt?                  // 最后更新人ID
  goods_attribute_sets   GoodsAttributeSet        @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_attribute_values GoodsAttributeValue[]

  @@index([deleted_at], map: "idx_goods_attribute_items_deleted_at")
  @@index([is_filterable], map: "idx_goods_attribute_items_filterable")
  @@index([is_enabled], map: "idx_goods_attribute_items_is_enabled")
  @@index([goods_attribute_set_id], map: "idx_goods_attribute_items_set_id")
  @@index([created_by], map: "idx_goods_attribute_items_created_by")
  @@index([updated_by], map: "idx_goods_attribute_items_updated_by")
  @@map("goods_attribute_items")
  @@schema("base")
}

model GoodsAttributeSetCategoryAssociation {
  goods_attribute_set_id BigInt                  // 属性集ID
  goods_category_id      BigInt                  // 商品分类ID
  goods_attribute_sets   GoodsAttributeSet @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  // goods_categories       GoodsCategory     @relation(fields: [goods_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_attribute_set_id, goods_category_id])
  @@index([goods_category_id], map: "idx_goods_attribute_set_category_associations_category_id")
  @@map("goods_attribute_set_category_associations")
  @@schema("base")
}

model GoodsAttributeSet {
  id                                        BigInt                                      @id // 主键ID
  name                                      String                                      @unique @db.VarChar(100) // 属性集名称
  sort_order                                Int                                         @default(0) // 排序顺序
  created_at                                BigInt                                      @default(0) // 创建时间戳（毫秒）
  updated_at                                BigInt                                      @default(0) // 更新时间戳（毫秒）
  deleted_at                                BigInt?                                     // 删除时间戳（毫秒，软删除）
  created_by                                BigInt?                                     // 创建人ID
  updated_by                                BigInt?                                     // 最后更新人ID
  goods_attribute_items                     GoodsAttributeItem[]
  goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_attribute_sets_deleted_at")
  @@map("goods_attribute_sets")
  @@schema("base")
}

model GoodsAttributeValue {
  id                      BigInt                @id // 主键ID
  goods_spu_id            BigInt                // 商品SPU ID
  goods_attribute_item_id BigInt                // 商品属性项ID
  value                   String?               // 属性值
  created_at              BigInt                @default(0) // 创建时间戳（毫秒）
  updated_at              BigInt                @default(0) // 更新时间戳（毫秒）
  goods_attribute_items   GoodsAttributeItem    @relation(fields: [goods_attribute_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_spus              GoodsSpu              @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([goods_spu_id, goods_attribute_item_id], map: "goods_attribute_values_goods_spu_id_goods_attribute_it_key")
  @@index([goods_attribute_item_id], map: "idx_goods_attribute_values_item_id")
  @@index([goods_spu_id], map: "idx_goods_attribute_values_spu_id")
  @@map("goods_attribute_values")
  @@schema("base")
}

model GoodsAttributeSetPlatformRelation {
  id                          BigInt    @id @default(autoincrement())
  attribute_set_id            BigInt
  channel_id                  BigInt
  platform_id                 BigInt
  store_id                    BigInt
  platform_attribute_set_id   String?   @db.VarChar(100)
  platform_attribute_set_name String?   @db.VarChar(255)
  source_type                 Int       @default(0) @db.SmallInt
  created_at                  BigInt
  updated_at                  BigInt

  @@unique([attribute_set_id, channel_id, platform_id, store_id], map: "goods_attribute_set_platform_relation_unique")
  @@index([channel_id], map: "idx_goods_attribute_set_platform_relation_channel_id")
  @@index([platform_id], map: "idx_goods_attribute_set_platform_relation_platform_id")
  @@index([store_id], map: "idx_goods_attribute_set_platform_relation_store_id")
  @@index([attribute_set_id], map: "idx_goods_attribute_set_platform_relation_attribute_set_id")
  @@index([source_type], map: "idx_goods_attribute_set_platform_relation_source_type")
  @@map("goods_attribute_set_platform_relations")
  @@schema("base")
}

model GoodsAttributeItemPlatformRelation {
  id                          BigInt    @id @default(autoincrement())
  attribute_item_id           BigInt
  channel_id                  BigInt
  platform_id                 BigInt
  store_id                    BigInt
  platform_attribute_item_id  String?   @db.VarChar(100)
  platform_attribute_item_name String?  @db.VarChar(255)
  source_type                 Int       @default(0) @db.SmallInt
  created_at                  BigInt
  updated_at                  BigInt

  @@unique([attribute_item_id, channel_id, platform_id, store_id], map: "goods_attribute_item_platform_relation_unique")
  @@index([channel_id], map: "idx_goods_attribute_item_platform_relation_channel_id")
  @@index([platform_id], map: "idx_goods_attribute_item_platform_relation_platform_id")
  @@index([store_id], map: "idx_goods_attribute_item_platform_relation_store_id")
  @@index([attribute_item_id], map: "idx_goods_attribute_item_platform_relation_attribute_item_id")
  @@index([source_type], map: "idx_goods_attribute_item_platform_relation_source_type")
  @@map("goods_attribute_item_platform_relations")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_brand_platform_relation.prisma
// 商品品牌平台关联模型
model GoodsBrandPlatformRelation {
  id                    BigInt    @id                                                                     // 主键ID，雪花算法生成
  goods_brand_id        BigInt                                                                           // 品牌ID，关联goods_brands表
  channel_id            BigInt                                                                           // 渠道ID，关联channel表
  platform_id           BigInt                                                                           // 平台ID，关联platform表
  store_id              BigInt                                                                           // 店铺ID，关联store表
  platform_brand_id     String?   @db.VarChar(100)                                                      // 第三方平台品牌ID
  platform_brand_name   String?   @db.VarChar(255)                                                      // 第三方平台品牌名称
  created_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 创建时间戳（毫秒）
  updated_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                          // 删除时间戳（毫秒，软删除）

  // 索引
  @@index([goods_brand_id], map: "idx_goods_brand_platform_relations_brand_id")
  @@index([channel_id], map: "idx_goods_brand_platform_relations_channel_id")
  @@index([platform_id], map: "idx_goods_brand_platform_relations_platform_id")
  @@index([store_id], map: "idx_goods_brand_platform_relations_store_id")
  @@index([deleted_at], map: "idx_goods_brand_platform_relations_deleted_at")
  @@index([goods_brand_id, channel_id, platform_id, store_id], map: "idx_goods_brand_platform_relations_unique")
  @@map("goods_brand_platform_relations")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_brand.prisma
// 商品品牌模型
model GoodsBrand {
  id                  BigInt    @id @default(autoincrement())
  name                String    @db.VarChar(100)               // 品牌名称
  logo_url            String?   @db.VarChar(255)               // 品牌logo URL
  description         String?                                   // 品牌描述
  source_type         Int?      @default(0)                    // 来源类型：0-系统创建，1-第三方创建
  created_channel_id  BigInt?                                   // 创建渠道ID（第三方品牌）
  created_platform_id BigInt?                                   // 创建平台ID（第三方品牌）
  created_store_id    BigInt?                                   // 创建店铺ID（第三方品牌）
  created_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at          BigInt?                                   // 删除时间戳（毫秒，软删除）
  created_by          BigInt?                                   // 创建人ID
  updated_by          BigInt?                                   // 最后更新人ID

  // 关联商品
  goods_spus  GoodsSpu[]

  @@index([deleted_at], map: "idx_goods_brands_deleted_at")
  @@index([created_by], map: "idx_goods_brands_created_by")
  @@index([updated_by], map: "idx_goods_brands_updated_by")
  @@index([source_type], map: "idx_goods_brands_source_type")
  @@index([created_channel_id], map: "idx_goods_brands_created_channel_id")
  @@index([created_platform_id], map: "idx_goods_brands_created_platform_id")
  @@index([created_store_id], map: "idx_goods_brands_created_store_id")
  @@map("goods_brands")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_categories.prisma
// 商品分类模型
model GoodsCategory {
  id                        BigInt    @id @default(autoincrement())    // 主键ID
  goods_parent_category_id  BigInt?                                     // 父分类ID，用于构建分类树结构
  name                      String                                      // 分类名称
  // 移除 slug 字段定义
  image_url                 String?                                     // 分类图片URL
  description               String?                                     // 分类描述
  meta_title                String?                                     // SEO标题
  meta_keywords             String?                                     // SEO关键词
  meta_description          String?                                     // SEO描述
  sort_order                Int       @default(0)                       // 排序顺序
  is_enabled                Int       @default(1)                       // 是否启用：1-启用，0-禁用
  level                     Int?                                        // 分类层级：1-一级分类，2-二级分类，3-三级分类
  created_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at                BigInt?                                     // 删除时间戳（毫秒，软删除）
  created_by                BigInt?                                     // 创建人ID
  updated_by                BigInt?                                     // 最后更新人ID
  // 第三方分类相关字段
  source_type               Int?      @default(0)                       // 来源类型：0-系统分类，1-第三方分类
  created_channel_id        BigInt?                                     // 创建时的渠道ID（第三方分类专用）
  created_platform_id       BigInt?                                     // 创建时的平台ID（第三方分类专用）
  created_store_id          BigInt?                                     // 创建时的店铺ID（第三方分类专用）
  // 移除 goods_spu_count 字段定义
  parent                                    GoodsCategory?                              @relation("CategoryToSubcategory", fields: [goods_parent_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subcategories                             GoodsCategory[]                             @relation("CategoryToSubcategory")
  // goods_category_platform_relations        GoodsCategoryPlatformRelation[]             // 分类平台关联关系（暂时注释避免循环引用）
  // goods_category_associations               GoodsCategoryAssociation[]
  // goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_categories_deleted_at")
  @@index([goods_parent_category_id], map: "idx_goods_categories_goods_parent_id")
  @@index([is_enabled], map: "idx_goods_categories_is_enabled")
  @@index([created_by], map: "idx_goods_categories_created_by")
  @@index([updated_by], map: "idx_goods_categories_updated_by")
  @@index([source_type], map: "idx_goods_categories_source_type")
  @@index([created_channel_id], map: "idx_goods_categories_created_channel_id")
  @@index([created_platform_id], map: "idx_goods_categories_created_platform_id")
  @@index([created_store_id], map: "idx_goods_categories_created_store_id")
  @@map("goods_categories")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_category_associations.prisma
// 商品分类关联模型（商品与分类的多对多关联表）
model GoodsCategoryAssociation {
  goods_spu_id      BigInt                                  // 商品SPU ID，关联goods_spus表
  goods_category_id BigInt                                  // 分类ID，关联goods_categories表
  is_primary        Boolean   @default(false)               // 是否主分类（每个商品只能有一个主分类）

  // 关联（多对多关系的中间表）
  goods_spu        GoodsSpu      @relation(fields: [goods_spu_id], references: [id])  // 关联到商品SPU
  // goods_category  GoodsCategory @relation(fields: [goods_category_id], references: [id])  // 关联到商品分类（暂时注释以避免循环引用）

  @@id([goods_spu_id, goods_category_id])  // 联合主键：SPU ID + 分类 ID
  @@index([goods_category_id], map: "idx_goods_category_associations_category_id")  // 分类ID索引
  @@map("goods_category_associations")  // 映射到数据库表名
  @@schema("base")  // 所属schema
}


// ../apps/master/prisma/models/goods/goods_category_platform_relation.prisma
// 商品分类平台关系模型（管理分类在不同渠道、平台、店铺的关系和同步状态）
model GoodsCategoryPlatformRelation {
  id                    BigInt    @id                                                               // 主键ID
  goods_category_id     BigInt                                                                      // 商品分类ID，关联goods_categories表
  channel_id            BigInt                                                                      // 渠道ID，关联channel表
  platform_id           BigInt                                                                      // 平台ID，支持分类在多平台使用
  store_id              BigInt                                                                      // 店铺ID，支持分类在多店铺使用
  platform_category_id  String?   @db.VarChar(100)                                                // 第三方平台的分类ID
  platform_category_name String?  @db.VarChar(255)                                                // 第三方平台的分类名称

  // 业务状态字段
  is_enabled            Int       @default(1)                                                       // 是否启用：1-启用，0-禁用
  sort_order            Int       @default(0)                                                       // 在该平台的排序
  
  // 审计字段
  created_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))        // 创建时间戳（毫秒）
  updated_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))        // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                     // 删除时间戳（毫秒，软删除）
  created_by            BigInt?                                                                     // 创建人ID
  updated_by            BigInt?                                                                     // 最后更新人ID

  // 关联关系（暂时注释掉，避免循环引用问题）
  // goods_category        GoodsCategory @relation(fields: [goods_category_id], references: [id])     // 关联到商品分类
  // channel               channel       @relation(fields: [channel_id], references: [id])            // 关联到渠道
  // platform              platform      @relation(fields: [platform_id], references: [id])          // 关联到平台
  // store                 store         @relation(fields: [store_id], references: [id])              // 关联到店铺

  @@map("goods_category_platform_relations")
  @@schema("base")
  @@index([goods_category_id], map: "idx_goods_category_platform_relations_category_id")
  @@index([channel_id], map: "idx_goods_category_platform_relations_channel_id")
  @@index([platform_id], map: "idx_goods_category_platform_relations_platform_id")
  @@index([store_id], map: "idx_goods_category_platform_relations_store_id")
  @@index([goods_category_id, channel_id, platform_id, store_id], map: "idx_goods_category_platform_relations_unique")
  @@index([is_enabled], map: "idx_goods_category_platform_relations_enabled")
  @@index([created_at], map: "idx_goods_category_platform_relations_created_at")
  @@index([deleted_at], map: "idx_goods_category_platform_relations_deleted_at")
}


// ../apps/master/prisma/models/goods/goods_delete_logs.prisma
// 商品删除日志模型
model GoodsDeleteLog {
  id                BigInt    @id @default(autoincrement()) // 主键ID
  goods_spu_id      BigInt                                  // 被删除的商品SPU ID
  goods_spu_data    String    @db.Text                      // 删除前的商品SPU数据，JSON格式
  goods_skus_data   String?   @db.Text                      // 删除前的商品SKU数据，JSON格式
  reason            String?                                 // 删除原因
  created_at        BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at        BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  created_by        BigInt?                                 // 创建人ID
  updated_by        BigInt?                                 // 最后更新人ID
  ip_address        String?   @db.VarChar(50)               // 操作IP地址

  @@index([goods_spu_id], map: "idx_goods_delete_logs_goods_spu_id")
  @@index([created_at], map: "idx_goods_delete_logs_created_at")
  @@index([created_by], map: "idx_goods_delete_logs_created_by")
  @@index([updated_by], map: "idx_goods_delete_logs_updated_by")
  @@map("goods_delete_logs")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_drafts.prisma
model GoodsDraft {
  id         BigInt   @id @default(autoincrement()) // 主键ID
  created_by BigInt?  // 创建人ID
  updated_by BigInt?  // 最后更新人ID
  content    String   @db.Text // 草稿内容（JSON格式字符串）
  created_at BigInt?  // 创建时间戳（毫秒）
  updated_at BigInt?  // 更新时间戳（毫秒）
  deleted_at BigInt?  // 删除时间戳（毫秒，软删除）

  @@map("goods_drafts")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_freight_templates.prisma
// 商品运费模板相关模型
// 更新日期：2025-06-13
// 三表结构：运费模板主表、运费配置表、区域关联表

// 运费模板主表
model GoodsFreightTemplates {
  id          BigInt   @id                                   // 主键ID
  name        String   @db.VarChar(100) @map("name")         // 运费模板名称 (必填)
  charge_type Int      @db.SmallInt @map("charge_type")      // 计价方式 (1: 按件数, 2: 按重量, 3: 按体积)
  created_at  BigInt   @map("created_at")                    // 创建时间戳（毫秒）
  updated_at  BigInt   @map("updated_at")                    // 更新时间戳（毫秒）
  deleted_at  BigInt?  @map("deleted_at")                    // 删除时间戳，null表示未删除
  created_by  BigInt?  @map("created_by")                    // 创建人ID
  updated_by  BigInt?  @map("updated_by")                    // 更新人ID

  // 关联的运费配置
  freight_configs GoodsFreightConfig[] @relation("template_configs")

  @@map("goods_freight_templates")                                          // 映射到数据库表名
  @@schema("base")                                                          // 所属schema
  @@unique([name, deleted_at], name: "unique_name_when_active")              // 在未删除状态下名称唯一
  @@index([deleted_at], name: "idx_goods_freight_templates_deleted_at")     // 软删除索引
  @@index([created_by], name: "idx_goods_freight_templates_created_by")     // 创建人索引
  @@index([updated_by], name: "idx_goods_freight_templates_updated_by")     // 更新人索引
}

// 运费配置表 (每个配置可以关联多个区域)
model GoodsFreightConfig {
  id                  BigInt   @id                                       // 主键ID
  freight_template_id BigInt   @map("freight_template_id")               // 关联的运费模板ID
  first_item          Int      @map("first_item")                        // 首件/首重/首体积数量
  first_fee           Decimal  @db.Decimal(10, 2) @map("first_fee")      // 首件/首重/首体积费用
  additional_item     Int      @map("additional_item")                   // 续件/续重/续体积数量
  additional_fee      Decimal  @db.Decimal(10, 2) @map("additional_fee") // 续件/续重/续体积费用
  is_default          Int      @default(0) @map("is_default")            // 是否为默认配置（1-默认，0-非默认）
  created_at          BigInt   @map("created_at")                        // 创建时间戳（毫秒）
  updated_at          BigInt   @map("updated_at")                        // 更新时间戳（毫秒）
  deleted_at          BigInt?  @map("deleted_at")                        // 删除时间戳
  created_by          BigInt?  @map("created_by")                        // 创建人ID
  updated_by          BigInt?  @map("updated_by")                        // 更新人ID
  
  // 关联运费模板
  freight_template    GoodsFreightTemplates @relation("template_configs", fields: [freight_template_id], references: [id], onDelete: Cascade)
  
  // 关联区域
  region_relations    GoodsFreightRegionRelation[] @relation("config_regions")

  @@map("goods_freight_configs")
  @@schema("base")
  @@index([freight_template_id], name: "idx_goods_freight_configs_template_id")
  @@index([deleted_at], name: "idx_goods_freight_configs_deleted_at")
  @@index([is_default], name: "idx_goods_freight_configs_is_default")
}

// 运费区域关联表
model GoodsFreightRegionRelation {
  id                  BigInt   @id                                 // 主键ID
  freight_config_id   BigInt   @map("freight_config_id")           // 关联的运费配置ID
  region_code         String   @db.VarChar(20) @map("region_code") // 区域编码
  region_name         String   @db.VarChar(50) @map("region_name") // 区域名称
  parent_name         String   @db.VarChar(50) @map("parent_name") // 上级区域名称
  created_at          BigInt   @map("created_at")                  // 创建时间戳（毫秒）
  updated_at          BigInt   @map("updated_at")                  // 更新时间戳（毫秒）
  deleted_at          BigInt?  @map("deleted_at")                  // 删除时间戳
  
  // 关联配置
  freight_config      GoodsFreightConfig @relation("config_regions", fields: [freight_config_id], references: [id], onDelete: Cascade)

  @@map("goods_freight_region_relations")
  @@schema("base")
  @@index([freight_config_id], name: "idx_goods_freight_region_relations_config_id")
  @@index([region_code], name: "idx_goods_freight_region_relations_region_code")
  @@index([deleted_at], name: "idx_goods_freight_region_relations_deleted_at")
}


// ../apps/master/prisma/models/goods/goods_images.prisma
// 商品图片模型
model GoodsImage {
  id          BigInt    @id @default(autoincrement())                                  // 主键ID
  goods_spu_id BigInt                                                                  // 商品SPU ID，关联goods_spus表
  goods_sku_id BigInt?                                                                 // SKU ID，关联goods_skus表，NULL表示是SPU的通用图片
  image_url    String                                                                  // 图片URL路径
  alt_text    String?                                                                  // 图片的替代文本描述，用于无障碍访问和SEO
  sort_order  Int       @default(0)                                                    // 图片排序序号，数字越小排序越靠前
  is_default  Boolean   @default(false)                                                // 是否为SPU的默认主图（展示在商品列表和详情页的主图）
  created_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 更新时间戳（毫秒）

  // 关联
  goods_spu   GoodsSpu  @relation(fields: [goods_spu_id], references: [id])            // 关联到商品SPU
  goods_sku   GoodsSku? @relation(fields: [goods_sku_id], references: [id])            // 关联到商品SKU（可选）

  @@index([goods_spu_id], map: "idx_goods_images_goods_spu_id")                        // SPU ID索引
  @@index([goods_sku_id], map: "idx_goods_images_goods_sku_id")                        // SKU ID索引
  @@map("goods_images")                                                                // 映射到数据库表名
  @@schema("base")                                                                      // 所属schema
}


// ../apps/master/prisma/models/goods/goods_service_associations.prisma
// 商品服务关联模型（多对多关联表）
model GoodsServiceAssociation {
  goods_spu_id      BigInt        // 商品SPU ID，关联goods_spus表
  goods_service_id  BigInt        // 服务ID，关联goods_services表

  // 关联（多对多关系的中间表）
  goods_spu         GoodsSpu      @relation(fields: [goods_spu_id], references: [id])      // 关联到商品SPU
  goods_service     GoodsService  @relation(fields: [goods_service_id], references: [id])  // 关联到商品服务

  @@id([goods_spu_id, goods_service_id])  // 联合主键
  @@index([goods_service_id], map: "idx_goods_service_associations_service_id")  // 服务ID索引
  @@map("goods_service_associations")  // 映射到数据库表名
  @@schema("base")  // 所属schema
}


// ../apps/master/prisma/models/goods/goods_service.prisma
// 商品服务模型
model GoodsService {
  id          BigInt    @id @default(autoincrement())
  name        String    @db.VarChar(100)               // 服务名称
  image_url   String?   @db.VarChar(255)               // 服务图标URL
  description String?                                   // 服务描述
  sort_order  Int       @default(0)                    // 排序顺序，数值越小越靠前
  created_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at  BigInt?                                   // 删除时间戳（毫秒，软删除）
  created_by  BigInt?                                   // 创建人ID
  updated_by  BigInt?                                   // 最后更新人ID

  // 关联
  goods_service_associations GoodsServiceAssociation[]

  @@index([deleted_at], map: "idx_goods_services_deleted_at")
  @@index([created_by], map: "idx_goods_services_created_by")
  @@index([updated_by], map: "idx_goods_services_updated_by")
  @@map("goods_services")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_sku_channel.prisma
// 商品SKU关联渠道模型（定义SKU与销售渠道的多对多关系）
model GoodsSkuChannel {
  id                   BigInt    @id @default(autoincrement())                               // 主键ID
  goods_sku_id         BigInt                                                               // 商品SKU ID，关联goods_skus表
  channel_id           BigInt                                                               // 渠道ID，关联channel表
  third_party_sku_code String?                                                              // 第三方SKU编码
  third_party_sku_id   String?   @db.VarChar(255)                                          // 第三方SKU ID
  third_party_spu_id   String?   @db.VarChar(255)                                          // 第三方SPU ID
  is_enabled           Int       @default(1) @db.SmallInt                                   // 是否启用：1-启用，0-禁用
  created_at           BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))   // 创建时间戳（毫秒）
  updated_at           BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))   // 更新时间戳（毫秒）
  deleted_at           BigInt?                                                              // 删除时间戳（毫秒，软删除）
  created_by           BigInt?                                                              // 创建者ID
  updated_by           BigInt?                                                              // 更新者ID

  // 关联
  goods_sku        GoodsSku            @relation(fields: [goods_sku_id], references: [id])  // 关联到商品SKU
  channel          channel             @relation(fields: [channel_id], references: [id])   // 关联到渠道

  @@index([goods_sku_id], map: "idx_goods_sku_channel_sku_id")                         // SKU ID索引
  @@index([channel_id], map: "idx_goods_sku_channel_channel_id")                       // 渠道ID索引
  @@index([deleted_at], map: "idx_goods_sku_channel_deleted_at")                       // 软删除索引
  @@unique([goods_sku_id, channel_id, deleted_at], name: "unique_sku_channel")         // 确保一个SKU在同一时间只能关联同一渠道一次
  @@unique([goods_sku_id, channel_id, third_party_sku_code], name: "unique_sku_channel_third_party_code")  // 确保系统SKU、渠道和第三方SKU编码的组合唯一
  @@map("goods_sku_channel")                                                           // 映射到数据库表名
  @@schema("base")                                                                      // 所属schema
}


// ../apps/master/prisma/models/goods/goods_sku_platform_relation.prisma
// SKU平台关系模型（管理SKU在不同渠道、平台、店铺的关系和同步状态）
model GoodsSkuPlatformRelation {
  id                    BigInt    @id                                                               // 主键ID
  goods_sku_id          BigInt                                                                      // 商品SKU ID，关联goods_skus表
  channel_id            BigInt                                                                      // 渠道ID，关联channel表
  platform_id           BigInt                                                                      // 平台ID，支持SKU在多平台销售
  store_id              BigInt                                                                      // 店铺ID，支持SKU在多店铺销售
  third_party_sku_code  String?   @db.VarChar(100)                                                 // 第三方SKU编码
  is_enabled            Int       @default(1) @db.SmallInt                                          // 是否启用：1-启用，0-禁用
  created_at            BigInt                                                                      // 创建时间戳（毫秒）
  updated_at            BigInt                                                                      // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                     // 删除时间戳（毫秒，软删除）

  // 平台相关字段
  platform_sku_id       String?   @db.VarChar(100)                                                 // 平台返回的SKU ID
  platform_status       Int?      @db.SmallInt                                                     // 平台状态
  platform_price        Decimal?  @db.Decimal(10, 2)                                               // 平台价格
  platform_stock        Int?                                                                       // 平台库存

  // 关联
  goods_sku             GoodsSku  @relation(fields: [goods_sku_id], references: [id], onDelete: Cascade, onUpdate: Cascade)  // 关联到商品SKU
  channel               channel   @relation(fields: [channel_id], references: [id], onDelete: Cascade, onUpdate: Cascade)    // 关联到渠道
  platform              platform  @relation(fields: [platform_id], references: [id], onDelete: Cascade, onUpdate: Cascade)  // 关联到平台
  store                 store     @relation(fields: [store_id], references: [id], onDelete: Cascade, onUpdate: Cascade)      // 关联到店铺

  // 索引
  @@index([channel_id, platform_id, store_id, is_enabled], name: "idx_goods_sku_platform_relation_composite")
  @@index([platform_id], name: "idx_goods_sku_platform_relation_platform")
  @@index([platform_sku_id], name: "idx_goods_sku_platform_relation_platform_sku_id")
  @@index([store_id], name: "idx_goods_sku_platform_relation_store")

  // 唯一约束
  @@unique([goods_sku_id, channel_id, platform_id, store_id], name: "idx_goods_sku_platform_relation_unique_combination")
  
  @@map("goods_sku_platform_relation")                                                             // 映射到数据库表名
  @@schema("base")                                                                                  // 所属schema
}


// ../apps/master/prisma/models/goods/goods_sku_specification_values.prisma
// 商品SKU规格值关联模型（关联表）
model GoodsSkuSpecificationValue {
  goods_sku_id                BigInt    // SKU ID，关联goods_skus表
  goods_specification_value_id BigInt    // 规格值ID，关联goods_specification_values表

  // 关联（多对多关系的中间表）
  goods_sku                   GoodsSku              @relation(fields: [goods_sku_id], references: [id])  // 关联到SKU
  goods_specification_value   GoodsSpecificationValue @relation(fields: [goods_specification_value_id], references: [id])  // 关联到规格值

  // 复合主键
  @@id([goods_sku_id, goods_specification_value_id])  // 联合主键
  @@index([goods_sku_id], map: "idx_goods_sku_spec_values_sku_id")
  @@index([goods_specification_value_id], map: "idx_goods_sku_specification_values_spec_value_id")  // 规格值ID索引
  @@map("goods_sku_specification_values")  // 映射到数据库表名
  @@schema("base")  // 所属schema
}


// ../apps/master/prisma/models/goods/goods_skus.prisma
// 商品SKU模型（定义具体的商品库存单元）
model GoodsSku {
  id                BigInt    @id @default(autoincrement())                                  // 主键ID
  goods_spu_id      BigInt                                                                  // 商品SPU ID，关联goods_spus表
  sku_code          String    @unique @db.VarChar(100)                                      // SKU编码，唯一标识
  barcode           String?   @db.VarChar(100)                                              // 商品条形码（可用于扫码识别）
  sales_price       Decimal   @db.Decimal(10, 2)                                            // 实际销售价格（元）
  market_price      Decimal?  @db.Decimal(10, 2)                                            // 市场参考价/原价（元）
  cost_price        Decimal?  @db.Decimal(10, 2)                                            // 商品成本价（元）
  weight            Decimal?  @db.Decimal(10, 2)                                            // 商品重量（单位：kg）
  volume            Decimal?  @db.Decimal(10, 2)                                            // 商品体积（单位：m³）
  stock             Int       @default(0)                                                   // 当前库存数量
  low_stock_threshold Int?                                                                  // 库存预警阈值（库存低于此值时发出预警）
  sales_volume      Int       @default(0)                                                   // 历史销售数量
  unit              String?   @db.VarChar(20)                                               // 销售单位（如件、个、箱等）
  is_enabled        Int       @default(1)                                                    // 是否启用：1-启用，0-禁用
  created_at        BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 创建时间戳（毫秒）
  updated_at        BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 更新时间戳（毫秒）
  deleted_at        BigInt?                                                                 // 删除时间戳（毫秒，软删除）

  // 关联
  goods_spu         GoodsSpu  @relation(fields: [goods_spu_id], references: [id])            // 关联到商品SPU（多对一关系）
  goods_images      GoodsImage[]                                                            // 商品SKU的图片（一对多关系）
  goods_sku_specification_values GoodsSkuSpecificationValue[]                               // 商品SKU的规格值关联（一对多关系）
  goods_sku_channels GoodsSkuChannel[]                                                     // 商品SKU关联的渠道（一对多关系）
  goods_sku_platform_relations GoodsSkuPlatformRelation[]                                  // 商品SKU平台关系（一对多关系）

  @@index([goods_spu_id], map: "idx_goods_skus_spu_id")                                    // SPU ID索引
  @@index([deleted_at], map: "idx_goods_skus_deleted_at")                                  // 软删除索引
  @@map("goods_skus")                                                                      // 映射到数据库表名
  @@schema("base")                                                                          // 所属schema
}


// ../apps/master/prisma/models/goods/goods_specification_names.prisma
// 商品规格名称模型（如颜色、尺寸等规格的名称）
model GoodsSpecificationName {
  id          BigInt    @id @default(autoincrement())                                  // 主键ID
  name        String    @unique @db.VarChar(100)                                       // 规格名称，如"颜色"、"尺寸"，唯一
  created_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))      // 更新时间戳（毫秒）
  deleted_at  BigInt?                                                                 // 删除时间戳（毫秒，软删除）

  // 关联（一对多关系：一个规格名称对应多个规格值）
  goods_specification_values GoodsSpecificationValue[]                                // 关联的规格值集合

  @@index([deleted_at], map: "idx_goods_specification_names_deleted_at")              // 软删除索引
  @@map("goods_specification_names")                                                  // 映射到数据库表名
  @@schema("base")                                                                      // 所属schema
}


// ../apps/master/prisma/models/goods/goods_specification_values.prisma
// 商品规格值模型（存储具体的规格值，如红色、XL等）
model GoodsSpecificationValue {
  id                         BigInt                @id @default(autoincrement())                 // 主键ID
  goods_specification_name_id BigInt                                                              // 规格名ID，关联goods_specification_names表
  value                      String                @db.VarChar(100)                               // 规格值，如"红色"、"XL"等
  created_at                 BigInt                @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at                 BigInt                @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at                 BigInt?                                                             // 删除时间戳（毫秒，软删除）

  // 关联
  goods_specification_name   GoodsSpecificationName @relation(fields: [goods_specification_name_id], references: [id])  // 关联到规格名称（多对一关系）
  goods_sku_specification_values GoodsSkuSpecificationValue[]                                   // 与SKU的关联（通过中间表）

  @@index([goods_specification_name_id], map: "idx_goods_specification_values_name_id")          // 规格名ID索引
  @@index([deleted_at], map: "idx_goods_specification_values_deleted_at")                        // 软删除索引
  @@map("goods_specification_values")                                                            // 映射到数据库表名
  @@schema("base")                                                                                // 所属schema
}


// ../apps/master/prisma/models/goods/goods_spus.prisma
// 商品SPU模型（标准产品单位）
model GoodsSpu {
  id                          BigInt                        @id @default(autoincrement()) // 主键ID
  spu_code                    String?                       @unique @db.VarChar(100)      // SPU编码，唯一
  name                        String                                                      // 商品名称
  subtitle                    String?                                                     // 商品副标题
  slug                        String                        @unique @db.VarChar(255)      // 商品URL别名，用于SEO
  description                 String?                                                     // 商品描述
  goods_brand_id              BigInt?                                                     // 品牌ID，关联goods_brands表
  goods_freight_template_id   BigInt?                                                     // 运费模板ID，关联goods_freight_templates表
  meta_title                  String?                                                     // SEO标题
  meta_keywords               String?                                                     // SEO关键词
  meta_description            String?                                                     // SEO描述
  sort_order                  Int                           @default(0)                   // 排序顺序
  status                      Int                           @default(0) @db.SmallInt      // 商品状态：0-草稿，1-上架，2-下架
  published_at                BigInt?                                                     // 发布时间戳（毫秒）
  is_virtual                  Int                           @default(0) @db.SmallInt      // 是否虚拟商品：0-否，1-是
  is_shipping_required        Int                           @default(1) @db.SmallInt      // 是否需要物流：0-否，1-是
  is_free_shipping            Int                           @default(1) @db.SmallInt      // 是否包邮：1-是，2-否
  delivery_area               String?                       @db.Text                      // 配送区域，JSON格式
  delivery_time               BigInt?                                                     // 配送时间（毫秒）
  created_at                  BigInt                        @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at                  BigInt                        @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at                  BigInt?                                                     // 删除时间戳（毫秒，软删除）
  created_by                  BigInt?                                                     // 创建人ID
  updated_by                  BigInt?                                                     // 最后更新人ID
  total_sales                 Int                           @default(0)                   // 总销量
  total_stock                 Int                           @default(0)                   // 总库存
  source_type                 Int?                                                        // 来源类型：0-系统订单，1-第三方订单
  created_channel_id          BigInt?                                                     // 创建渠道ID
  created_platform_id         BigInt?                                                     // 创建平台ID
  created_store_id            BigInt?                                                     // 创建店铺ID

  // 关联 - 注释掉尚未定义的模型关联
  // goods_associations_goods_associations_source_goods_spu_idTogoods_spus GoodsAssociation[]          @relation("goods_associations_source_goods_spu_idTogoods_spus")
  // goods_associations_goods_associations_target_goods_spu_idTogoods_spus GoodsAssociation[]          @relation("goods_associations_target_goods_spu_idTogoods_spus")
  goods_attribute_values                                                GoodsAttributeValue[]
  goods_category_associations                                           GoodsCategoryAssociation[]
  goods_images                                                          GoodsImage[]
  goods_service_associations                                            GoodsServiceAssociation[]    // 添加服务关联
  goods_skus                                                            GoodsSku[]
  goods_brands                                                          GoodsBrand?                 @relation(fields: [goods_brand_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_goods_spus_brand")
  // goods_freight_templates                                               GoodsFreightTemplate?      @relation(fields: [goods_freight_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_goods_spus_freight_template")
  goods_tag_associations                                                GoodsTagAssociation[]       // 添加标签关联
  goods_videos                                                          GoodsVideo[]

  @@index([created_at], map: "idx_goods_spus_created_at")
  @@index([deleted_at], map: "idx_goods_spus_deleted_at")
  @@index([goods_brand_id], map: "idx_goods_spus_goods_brand_id")
  @@index([goods_freight_template_id], map: "idx_goods_spus_goods_freight_template_id")
  @@index([slug], map: "idx_goods_spus_slug")
  @@index([status], map: "idx_goods_spus_status")
  @@index([created_by], map: "idx_goods_spus_created_by")
  @@index([updated_by], map: "idx_goods_spus_updated_by")
  @@map("goods_spus")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_tags.prisma
model GoodsTagAssociation {
  goods_spu_id BigInt    // 商品SPU ID
  goods_tag_id BigInt    // 商品标签ID
  // 恢复与GoodsSpu的关系
  goods_spus   GoodsSpu @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_tags   GoodsTag @relation(fields: [goods_tag_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_spu_id, goods_tag_id])
  @@index([goods_tag_id], map: "idx_goods_tag_associations_tag_id")
  @@map("goods_tag_associations")
  @@schema("base")
}

model GoodsTag {
  id                     BigInt                   @id @default(autoincrement()) // 主键ID
  name                   String                   @unique @db.VarChar(100) // 标签名称
  slug                   String                   @unique @db.VarChar(100) // 标签别名（URL友好）
  image_url              String?                  // 标签图片URL
  description            String?                  // 标签描述
  tag_type               String?                  @db.VarChar(50) // 标签类型
  sort_order             Int                      @default(0) // 排序顺序
  is_enabled             Boolean                  @default(true) // 是否启用
  created_at             BigInt                   // 创建时间戳（毫秒）
  updated_at             BigInt                   // 更新时间戳（毫秒）
  deleted_at             BigInt?                  // 删除时间戳（毫秒，软删除）
  goods_spu_count        Int                      @default(0) // 关联商品数量
  created_by             BigInt?                  // 创建人ID
  updated_by             BigInt?                  // 最后更新人ID
  goods_tag_associations GoodsTagAssociation[]

  @@index([deleted_at], map: "idx_goods_tags_deleted_at")
  @@index([is_enabled], map: "idx_goods_tags_is_enabled")
  @@index([slug], map: "idx_goods_tags_slug")
  @@index([created_by], map: "idx_goods_tags_created_by")
  @@index([updated_by], map: "idx_goods_tags_updated_by")
  @@map("goods_tags")
  @@schema("base")
}


// ../apps/master/prisma/models/goods/goods_videos.prisma
model GoodsVideo {
  id              BigInt     @id @default(autoincrement()) // 主键ID
  goods_spu_id    BigInt     // 商品SPU ID
  video_url       String     // 视频URL
  cover_image_url String?    // 视频封面图URL
  sort_order      Int        @default(0) // 排序顺序
  created_at      BigInt     @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at      BigInt     @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  // 关联
  goods_spu       GoodsSpu   @relation(fields: [goods_spu_id], references: [id])

  @@index([goods_spu_id], map: "idx_goods_videos_goods_spu_id")
  @@map("goods_videos")
  @@schema("base")
}


// ../apps/master/prisma/models/invoices.prisma
/// 发票记录表，存储已开具的发票信息
model invoices {
  // 主键
  id                    BigInt                   @id /// 发票ID，主键，16位雪花算法
  application_id        BigInt                   /// 关联的申请记录ID
  order_id              BigInt                   /// 关联的订单ID（冗余字段，便于查询）

  // 基本发票信息
  invoice_number        String?                  @db.VarChar(100) /// 发票号码（可为空）
  invoice_code          String?                  @db.VarChar(50) /// 发票代码（可为空）
  invoice_type          Int                      @default(1) /// 发票类型：1-电子发票（默认），2-纸质发票
  invoice_amount        Decimal                  @db.Decimal(12, 2) /// 发票金额
  tax_amount            Decimal?                 @db.Decimal(12, 2) /// 税额（可为空）

  // 发票状态
  invoice_status        Int                      @default(0) /// 发票状态：0-已开具，1-已发送，2-已作废

  // 票面信息（都可为空）
  header_name           String?                  @db.VarChar(200) /// 发票抬头名称
  tax_number            String?                  @db.VarChar(50) /// 纳税人识别号
  company_address       String?                  @db.VarChar(500) /// 公司地址
  company_phone         String?                  @db.VarChar(50) /// 公司电话
  bank_name             String?                  @db.VarChar(200) /// 开户银行
  bank_account          String?                  @db.VarChar(50) /// 银行账户

  // 文件信息（可为空）
  file_url              String?                  @db.Text /// 发票文件URL

  // 开具信息（可为空）
  issued_by             BigInt?                  /// 开具人ID
  issued_at             BigInt?                  /// 开具时间

  // 作废信息（可为空）
  voided_by             BigInt?                  /// 作废人ID
  voided_at             BigInt?                  /// 作废时间
  void_reason           String?                  @db.VarChar(500) /// 作废原因

  // 备注
  remark                String?                  @db.VarChar(500) /// 备注信息
  
  // 审计字段
  created_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at            BigInt?                  /// 删除时间戳（毫秒），软删除
  created_by            BigInt?                  /// 创建人ID
  updated_by            BigInt?                  /// 更新人ID

  // 索引
  @@index([application_id], map: "idx_invoices_application_id")
  @@index([order_id], map: "idx_invoices_order_id")
  @@index([invoice_number], map: "idx_invoices_invoice_number")
  @@index([invoice_type], map: "idx_invoices_invoice_type")
  @@index([invoice_status], map: "idx_invoices_invoice_status")
  @@index([deleted_at], map: "idx_invoices_deleted_at")
  @@index([created_at], map: "idx_invoices_created_at")
  @@index([order_id, deleted_at], map: "idx_invoices_order_deleted")
  @@index([application_id, invoice_status, deleted_at], map: "idx_invoices_app_status")
  @@index([invoice_type, invoice_status, deleted_at], map: "idx_invoices_type_status")
  @@index([header_name], map: "idx_invoices_header_name")
  @@index([tax_number], map: "idx_invoices_tax_number")

  @@map("invoices")
  @@schema("base")
}


// ../apps/master/prisma/models/mall_banner.prisma
/// 商城轮播图表，存储轮播图信息
model MallBanner {
  // 主键
  id            BigInt    @id                         /// @db.Comment('轮播图ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  title         String    @db.VarChar(100)            /// @db.Comment('轮播图标题，必填')
  image_url     String    @db.VarChar(500)            /// @db.Comment('图片地址，必填')
  sort_order    Int       @default(0) @db.SmallInt    /// @db.Comment('排序，数字越小越靠前，默认0')
  link_url      String?   @db.VarChar(500)            /// @db.Comment('跳转网址，可为空')
  link_type     Int       @default(3) @db.SmallInt    /// @db.Comment('网址类型：1-内部链接，2-外部链接，3-无链接，默认3')
  status        Int       @default(1) @db.SmallInt    /// @db.Comment('状态：1-启用，0-禁用，默认1')
  remark        String?   @db.Text                    /// @db.Comment('备注信息')
  
  // 审计字段
  created_at    BigInt                                /// @db.Comment('创建时间戳（毫秒）')
  updated_at    BigInt                                /// @db.Comment('更新时间戳（毫秒）')
  created_by    BigInt?                               /// @db.Comment('创建人ID')
  updated_by    BigInt?                               /// @db.Comment('更新人ID')
  deleted_at    BigInt?                               /// @db.Comment('删除时间戳（毫秒），空表示未删除')

  @@map("mall_banner")
  @@schema("base")
}


// ../apps/master/prisma/models/mall_invoice_headers.prisma
/// 商城发票抬头表，存储用户的发票抬头信息
model mall_invoice_headers {
  // 主键
  id                    BigInt                   @id /// 发票抬头ID，主键，16位雪花算法

  // 基本信息
  user_id               BigInt                   /// 用户ID
  header_type           Int                      /// 抬头类型：1-个人抬头，2-企业抬头
  header_name           String                   @db.VarChar(200) /// 发票抬头名称
  
  // 企业信息（企业抬头时使用）
  tax_number            String?                  @db.VarChar(50) /// 纳税人识别号
  company_address       String?                  @db.VarChar(500) /// 公司地址
  company_phone         String?                  @db.VarChar(50) /// 公司电话
  bank_name             String?                  @db.VarChar(200) /// 开户银行
  bank_account          String?                  @db.VarChar(50) /// 银行账户
  
  // 状态信息
  is_default            Boolean                  @default(false) /// 是否默认抬头
  
  // 审计字段
  created_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at            BigInt?                  /// 删除时间戳（毫秒），软删除
  created_by            BigInt?                  /// 创建人ID
  updated_by            BigInt?                  /// 更新人ID

  // 索引
  @@index([user_id], map: "idx_mall_invoice_headers_user_id")
  @@index([header_type], map: "idx_mall_invoice_headers_header_type")
  @@index([is_default], map: "idx_mall_invoice_headers_is_default")
  @@index([deleted_at], map: "idx_mall_invoice_headers_deleted_at")
  @@index([user_id, deleted_at], map: "idx_mall_invoice_headers_user_deleted")
  @@index([user_id, header_type, deleted_at], map: "idx_mall_invoice_headers_user_type")

  @@map("mall_invoice_headers")
  @@schema("base")
}


// ../apps/master/prisma/models/mall_news_articles.prisma
// 商城新闻文章表
model mall_news_articles {
  id                        BigInt                @id @default(autoincrement()) // 文章ID，主键，自增长
  
  // 基本信息
  title                     String                @db.VarChar(200) // 文章标题，最大200字符
  summary                   String?               @db.VarChar(500) // 文章摘要，最大500字符
  content                   String                @db.Text // 文章内容
  
  // 分类关联
  category_id               BigInt                // 关联新闻分类ID
  category                  mall_news_categories  @relation(fields: [category_id], references: [id])
  
  // 媒体资源
  image_url                 String?               @db.VarChar(500) // 封面图片URL
  
  // 排序和状态
  sort_order                Int                   @default(0) // 排序权重，数值越大越靠前
  is_enabled                Int                   @default(1) // 启用状态：1-启用，0-禁用
  status                    Int                   @default(1) // 文章状态：1-正常，0-隐藏，-1-删除
  
  // SEO相关
  seo_title                 String?               @db.VarChar(200) // SEO标题
  seo_keywords              String?               @db.VarChar(500) // SEO关键词
  seo_description           String?               @db.VarChar(500) // SEO描述
  
  // 统计信息
  view_count                Int                   @default(0) // 浏览次数
  
  // 审计字段
  created_at                BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间戳（毫秒）
  updated_at                BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间戳（毫秒）
  deleted_at                BigInt?               // 删除时间戳（毫秒，软删除）
  
  // 操作人信息
  created_by                BigInt                // 创建人ID
  updated_by                BigInt                // 最后更新人ID

  @@schema("master")
  @@index([category_id], map: "idx_mall_news_articles_category")
  @@index([is_enabled], map: "idx_mall_news_articles_enabled")
  @@index([status], map: "idx_mall_news_articles_status")
  @@index([deleted_at], map: "idx_mall_news_articles_deleted_at")
  @@index([sort_order], map: "idx_mall_news_articles_sort")
  @@index([created_at], map: "idx_mall_news_articles_created_at")
  @@index([view_count], map: "idx_mall_news_articles_view_count")
}


// ../apps/master/prisma/models/mall_news_categories.prisma
// 商城新闻分类表
model mall_news_categories {
  id                        BigInt                @id @default(autoincrement()) // 分类ID，主键，自增长
  name                      String                @db.VarChar(100) // 分类名称，最大100字符
  description               String?               @db.Text // 分类描述
  
  // 层级结构
  parent_id                 BigInt?               // 父分类ID，null表示顶级分类
  level                     Int                   @default(1) // 分类层级，1为顶级
  sort_order                Int                   @default(0) // 排序权重，数值越大越靠前
  
  // 分类图片
  image_url                 String?               @db.VarChar(500) // 分类图片URL
  
  // 状态管理
  is_enabled                Int                   @default(1) // 启用状态：1-启用，0-禁用
  status                    Int                   @default(1) // 分类状态：1-正常，0-隐藏，-1-删除
  
  // SEO相关
  seo_title                 String?               @db.VarChar(200) // SEO标题
  seo_keywords              String?               @db.VarChar(500) // SEO关键词
  seo_description           String?               @db.Text // SEO描述
  
  // 统计字段
  news_count                Int                   @default(0) // 该分类下的新闻数量
  view_count                Int                   @default(0) // 分类查看次数
  
  // 审计字段
  created_at                BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_at                BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  deleted_at                BigInt?               // 删除时间（时间戳，毫秒），用于软删除
  created_by                BigInt?               // 创建人ID
  updated_by                BigInt?               // 更新人ID

  // 自关联关系
  parent                    mall_news_categories? @relation("CategoryParent", fields: [parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children                  mall_news_categories[] @relation("CategoryParent")

  // 关联新闻文章
  articles                  mall_news_articles[]

  // 索引
  @@index([parent_id], map: "idx_mall_news_categories_parent_id")
  @@index([is_enabled], map: "idx_mall_news_categories_is_enabled")
  @@index([status], map: "idx_mall_news_categories_status")
  @@index([sort_order], map: "idx_mall_news_categories_sort_order")
  @@index([level], map: "idx_mall_news_categories_level")
  @@index([created_at], map: "idx_mall_news_categories_created_at")
  @@index([deleted_at], map: "idx_mall_news_categories_deleted_at")

  @@map("mall_news_categories")
  @@schema("base")
}


// ../apps/master/prisma/models/mall_user_level.prisma
model MallUserLevel {
  id                BigInt    @id
  level_name        String    @db.VarChar(50)
  experience_value  BigInt    @default(0)
  total_consumption Decimal   @default(0) @db.Decimal(10, 2)
  level_icon        String?   @db.VarChar(255)
  discount_rate     Int       @default(100) @db.SmallInt
  is_system         Int       @default(0) @db.SmallInt
  status            Int       @default(1) @db.SmallInt
  remark            String?   @db.Text
  created_at        BigInt?
  updated_at        BigInt?
  deleted_at        BigInt?
  created_by        BigInt?
  updated_by        BigInt?

  // 关联用户表
  users MasterMallUser[] @relation("UserToLevel")

  @@map("mall_user_level")
  @@schema("base")
}

// ../apps/master/prisma/models/mall/mall_invoice_applications.prisma
/// 商城发票申请记录表，存储商城订单的发票申请信息和抬头快照
model mall_invoice_applications {
  // 主键
  id                    BigInt                   @id /// 申请记录ID，主键，16位雪花算法

  // 基本信息
  order_id              BigInt                   /// 关联的订单ID
  user_id               BigInt                   /// 申请用户ID
  header_id             BigInt?                  /// 关联的发票抬头ID（可为空）
  
  // 申请信息
  application_type      Int                      /// 申请类型：1-个人发票，2-企业发票
  application_amount    Decimal                  @db.Decimal(12, 2) /// 申请金额
  application_status    Int                      @default(0) /// 申请状态：0-待审核，1-已通过，2-已拒绝，3-已撤销
  
  // 发票抬头快照字段（申请时的抬头信息）
  snapshot_header_type  Int?                     /// 快照-抬头类型：1-个人抬头，2-企业抬头
  snapshot_header_name  String?                  @db.VarChar(200) /// 快照-发票抬头名称
  snapshot_tax_number   String?                  @db.VarChar(50) /// 快照-纳税人识别号
  snapshot_company_address String?              @db.VarChar(500) /// 快照-公司地址
  snapshot_company_phone String?                @db.VarChar(50) /// 快照-公司电话
  snapshot_bank_name    String?                  @db.VarChar(200) /// 快照-开户银行
  snapshot_bank_account String?                  @db.VarChar(50) /// 快照-银行账户
  
  // 申请信息
  application_reason    String?                  @db.VarChar(500) /// 申请原因/备注
  applicant_remark      String?                  @db.VarChar(500) /// 申请人备注
  
  // 审核信息
  reviewed_by           BigInt?                  /// 审核人ID
  reviewed_at           BigInt?                  /// 审核时间
  review_remark         String?                  @db.VarChar(500) /// 审核备注
  
  // 审计字段
  created_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at            BigInt?                  /// 删除时间戳（毫秒），软删除
  created_by            BigInt?                  /// 创建人ID
  updated_by            BigInt?                  /// 更新人ID

  // 索引
  @@index([order_id], map: "idx_mall_invoice_applications_order_id")
  @@index([user_id], map: "idx_mall_invoice_applications_user_id")
  @@index([header_id], map: "idx_mall_invoice_applications_header_id")
  @@index([application_type], map: "idx_mall_invoice_applications_type")
  @@index([application_status], map: "idx_mall_invoice_applications_status")
  @@index([deleted_at], map: "idx_mall_invoice_applications_deleted_at")
  @@index([created_at], map: "idx_mall_invoice_applications_created_at")
  @@index([order_id, deleted_at], map: "idx_mall_invoice_applications_order_deleted")
  @@index([user_id, application_status, deleted_at], map: "idx_mall_invoice_applications_user_status")
  @@index([application_type, application_status, deleted_at], map: "idx_mall_invoice_applications_type_status")

  @@map("mall_invoice_applications")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_tag_group.prisma
// 商城会员标签组模型
/// 商城会员标签组表，用于对标签进行分类管理
model MallTagGroup {
  /// 标签组ID，16位雪花算法，系统自动生成
  id                BigInt   @id 
  /// 标签组名称，必填，同一子站点下唯一
  tag_group_name    String   
  /// 标签组排序，数字越大越靠前
  tag_group_sort    Int      @default(0)
  /// 是否系统内置：1-是，0-否
  tag_group_buildin Int      @default(0) @db.SmallInt
  /// 是否启用：1-启用，0-禁用
  tag_group_enable  Int      @default(1) @db.SmallInt
  /// 子站点ID，为空表示适用于所有子站点
  subsite_id        BigInt?  
  /// 状态：1-正常，0-禁用
  status            Int      @default(1) 
  /// 创建时间戳（毫秒）
  created_at        BigInt   
  /// 更新时间戳（毫秒）
  updated_at        BigInt   
  /// 创建人ID
  created_by        BigInt?  
  /// 更新人ID
  updated_by        BigInt?  
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at        BigInt?  
  /// 备注信息
  remark            String?  @db.Text

  // 关联标签
  tags              MallTag[]

  // 设置唯一约束
  @@unique([tag_group_name, subsite_id])
  @@map("mall_tag_group")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_tag.prisma
// 商城会员标签模型
/// 商城会员标签表，存储会员标签信息
model MallTag {
  /// 标签ID，16位雪花算法，系统自动生成
  id           BigInt   @id
  /// 标签名称，必填，同一标签组和子站点下唯一
  tag_title    String   @db.Text
  /// 所属标签组ID
  tag_group_id BigInt
  /// 标签排序，数字越大越靠前
  tag_sort     Int      @default(0)
  /// 是否系统内置：1-是，0-否
  tag_buildin  Int      @default(0) @db.SmallInt
  /// 是否启用：1-启用，0-禁用
  tag_enable   Int      @default(1) @db.SmallInt
  /// 子站点ID，为空表示适用于所有子站点
  subsite_id   BigInt?
  /// 状态：1-正常，0-禁用
  status       Int      @default(1)
  /// 创建时间戳（毫秒）
  created_at   BigInt
  /// 更新时间戳（毫秒）
  updated_at   BigInt
  /// 创建人ID
  created_by   BigInt?
  /// 更新人ID
  updated_by   BigInt?
  /// 删除时间戳（毫秒），空表示未删除
  deleted_at   BigInt?
  /// 备注信息
  remark       String?  @db.Text

  // 关联标签组
  tag_group    MallTagGroup @relation(fields: [tag_group_id], references: [id], onUpdate: Cascade, onDelete: Restrict, map: "fk_mall_tag_group_id")

  // 关联用户表（多对多）
  user_tags MallUserTagAssociation[] @relation("TagToUsers")

  // 设置唯一约束
  @@unique([tag_title, tag_group_id, subsite_id])
  @@map("mall_tag")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_user_favorite.prisma
// 用户收藏表，记录用户收藏的商品、店铺等信息
model MallUserFavorite {
  id          BigInt    @id @default(autoincrement()) @map("id") @db.BigInt // 收藏ID，自增序列
  userId      BigInt    @map("user_id") @db.BigInt // 用户ID，关联mall_user表，用户删除时级联删除
  targetId    BigInt    @map("target_id") @db.BigInt // 收藏目标ID，如商品ID、店铺ID等
  targetType  Int       @map("target_type") @db.SmallInt // 收藏目标类型：1-商品，2-店铺，3-品牌，4-活动
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz() // 创建时间，自动记录当前时间
  updatedAt   DateTime  @default(now()) @map("updated_at") @db.Timestamptz() // 更新时间，自动记录当前时间
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz() // 删除时间，非空表示已删除
  remark      String?   @map("remark") @db.Text // 备注信息

  // 关联用户表
  user        MasterMallUser  @relation("UserToFavorites", fields: [userId], references: [id], onDelete: Cascade)

  // 唯一约束：同一用户对同一目标只能收藏一次（考虑软删除）
  @@unique([userId, targetId, targetType, deletedAt], name: "unique_user_target")

  // 索引
  @@index([userId, deletedAt], name: "idx_user_id_deleted")
  @@index([targetType, deletedAt], name: "idx_target_type_deleted")
  @@index([createdAt(sort: Desc)], name: "idx_created_at")

  @@map("mall_user_favorite")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_user_realname_auth.prisma
/// 商城用户实名认证记录表
model MallUserRealnameAuth {
  id                  BigInt    @id                         /// @db.Comment('认证记录ID，16位雪花算法，系统自动生成')
  user_id             BigInt    @unique                     /// @db.Comment('用户ID，关联 mall_user.id')
  type                Int       @db.SmallInt                /// @db.Comment('用户类型：0 个人用户 1 企业用户')
  auth_status         Int       @default(0) @db.SmallInt    /// @db.Comment('认证状态：0 未认证 1 等待认证 2 认证通过 3 认证失败')
  auth_date           DateTime? @db.Timestamptz             /// @db.Comment('认证通过时间')
  real_name           String?   @db.VarChar(100)            /// @db.Comment('真实姓名或企业名称')
  identity_no         String?   @db.VarChar(50)             /// @db.Comment('身份证号码或营业执照号码')
  auth_phone          String?   @db.VarChar(20)             /// @db.Comment('用于认证的手机号 (实名认证四要素之一)')
  bank_card_no        String?   @db.VarChar(30)             /// @db.Comment('银行卡号 (实名认证四要素之一)')
  id_card_front_url   String?   @db.Text                    /// @db.Comment('身份证正面照 URL (个人)')
  id_card_back_url    String?   @db.Text                    /// @db.Comment('身份证反面照 URL (个人)')
  id_card_in_hand_url String?   @db.Text                    /// @db.Comment('手持身份证照片 URL (个人)')
  license_url         String?   @db.Text                    /// @db.Comment('营业执照 URL (企业)')
  contact_person      String?   @db.VarChar(50)             /// @db.Comment('企业联系人姓名 (企业)')
  contact_mobile      String?   @db.VarChar(20)             /// @db.Comment('企业联系人手机号码 (企业)')
  contact_email       String?   @db.VarChar(100)            /// @db.Comment('企业联系人邮箱 (企业)')
  audit_remark        String?   @db.Text                    /// @db.Comment('审核备注或认证失败原因')
  created_at          BigInt                               /// @db.Comment('创建时间戳（毫秒）')
  updated_at          BigInt                               /// @db.Comment('更新时间戳（毫秒）')
  deleted_at          BigInt?                              /// @db.Comment('删除时间戳（毫秒），空表示未删除')

  // 关联用户表
  user                MasterMallUser @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Cascade, name: "UserToRealnameAuth")

  @@index([user_id], name: "idx_mall_user_realname_auth_user_id")
  @@index([auth_status], name: "idx_mall_user_realname_auth_status")
  @@index([identity_no], name: "idx_mall_user_realname_auth_identity_no")
  @@index([auth_phone], name: "idx_mall_user_realname_auth_auth_phone")
  @@index([bank_card_no], name: "idx_mall_user_realname_auth_bank_card_no")
  @@map("mall_user_realname_auth")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_user_tag_association.prisma
// 商城用户标签关联模型
/// 商城用户标签关联表，用户与标签的多对多关系
model MallUserTagAssociation {
  /// 用户ID，关联mall_user表
  user_id    BigInt
  /// 标签ID，关联mall_tag表
  tag_id     BigInt
  /// 创建时间戳（毫秒）
  created_at BigInt
  /// 创建人ID
  created_by BigInt?

  // 关联用户表
  user MasterMallUser @relation("UserToTags", fields: [user_id], references: [id], onDelete: Cascade)
  
  // 关联标签表
  tag MallTag @relation("TagToUsers", fields: [tag_id], references: [id], onDelete: Cascade)

  // 联合主键
  @@id([user_id, tag_id])
  
  // 索引
  @@index([user_id])
  @@index([tag_id])
  
  @@map("mall_user_tag_association")
  @@schema("base")
}


// ../apps/master/prisma/models/mall/mall_user.prisma
/// 商城前端用户表，存储商城用户数据，与系统用户无关联
model MasterMallUser {
  // 主键
  id            BigInt    @id                         /// @db.Comment('用户ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  username      String    @unique                    /// @db.Comment('用户名，必填，唯一，用于登录')
  password      String                               /// @db.Comment('密码，必填，加密存储')
  nickname      String?                              /// @db.Comment('用户昵称')
  avatar        String?                              /// @db.Comment('头像地址')
  phone         String?   @db.VarChar(11)            /// @db.Comment('手机号')
  email         String?   @unique                    /// @db.Comment('邮箱，唯一，可用于找回密码')
  
  // 状态信息
  status        Int       @default(1)                /// @db.Comment('状态：1-正常，0-禁用')
  realname_auth_status Int @default(0)              /// @db.Comment('实名认证状态：0-未认证，1-已提交待审核，2-已认证，3-认证失败')
  
  // 登录信息
  last_login_ip      String?   @db.VarChar(45)       /// @db.Comment('最后登录IP')
  last_login_time    BigInt?                         /// @db.Comment('最后登录时间戳（毫秒）')
  login_count        Int       @default(0)           /// @db.Comment('登录次数')
  
  // 审计字段
  created_at    BigInt                               /// @db.Comment('创建时间戳（毫秒）')
  updated_at    BigInt                               /// @db.Comment('更新时间戳（毫秒）')
  created_by    BigInt?                              /// @db.Comment('创建人ID')
  updated_by    BigInt?                              /// @db.Comment('更新人ID')
  deleted_at    BigInt?                              /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark        String?                              /// @db.Comment('备注信息')

  // 第三方登录信息
  wechat_openid String?                              /// @db.Comment('微信用户唯一标识，用于微信扫码登录')

  // 用户等级
  level_id      BigInt?                              /// @db.Comment('用户等级ID，关联mall_user_level表')

  // 关联实名认证表
  realname_auth MallUserRealnameAuth? @relation("UserToRealnameAuth")

  // 关联收藏表
  favorites MallUserFavorite[] @relation("UserToFavorites")

  // 关联用户等级表
  level MallUserLevel? @relation("UserToLevel", fields: [level_id], references: [id])

  // 关联用户标签表（多对多）
  user_tags MallUserTagAssociation[] @relation("UserToTags")

  // 索引
  @@index([status], name: "idx_mall_user_status")
  @@index([deleted_at], name: "idx_mall_user_deleted_at")
  @@index([wechat_openid], name: "idx_mall_user_wechat_openid")
  @@index([phone], name: "idx_mall_user_phone")

  @@map("mall_user")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_followers.prisma
model order_followers {
  id          BigInt    @id
  order_id    BigInt
  follower_id BigInt
  created_at  BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  updated_at  BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  deleted_at  BigInt?
  created_by  BigInt?
  updated_by  BigInt?
  deleted_by  BigInt?
  
  order       orders    @relation(fields: [order_id], references: [id], onDelete: Cascade)

  @@unique([order_id, follower_id], map: "idx_order_followers_unique")
  @@index([order_id], map: "idx_order_followers_order_id")
  @@index([follower_id], map: "idx_order_followers_follower_id")
  @@index([deleted_at], map: "idx_order_followers_deleted_at")
  @@index([created_by], map: "idx_order_followers_created_by")
  @@index([updated_by], map: "idx_order_followers_updated_by")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_item_reviews.prisma
// 订单商品评价表 - 以商品为单位进行评价
model order_item_reviews {
  id                    BigInt                @id @default(autoincrement()) // 评价ID，主键，自增长
  order_id              BigInt                // 关联的订单ID
  order_item_id         BigInt                // 关联的订单商品项ID
  user_id               BigInt                // 评价用户ID
  goods_spu_id          BigInt?               // 商品SPU ID
  goods_sku_id          BigInt?               // 商品SKU ID
  
  // 评分字段
  quality_rating        Int                   @default(5) // 商品质量评分：1-5星，默认5星
  service_rating        Int                   @default(5) // 服务态度评分：1-5星，默认5星
  logistics_rating      Int                   @default(5) // 物流速度评分：1-5星，默认5星
  overall_rating        Int                   @default(5) // 综合评分：1-5星，默认5星
  
  // 评价内容
  review_content        String?               @db.Text // 评价内容，10-500字，可选
  review_content_filtered String?             @db.Text // 过滤后的评价内容
  
  // 追加评价
  is_additional         Boolean               @default(false) // 是否为追加评价，默认否
  additional_content    String?               @db.Text // 追加评价内容
  additional_content_filtered String?         @db.Text // 过滤后的追加评价内容
  additional_at         BigInt?               // 追加评价时间
  
  // 评价设置
  is_anonymous          Boolean               @default(false) // 是否匿名评价，默认否
  is_evaluated          Boolean               @default(true) // 是否已评价，默认是
  
  // 状态管理
  status                Int                   @default(1) // 评价状态：1-正常显示，0-隐藏，-1-删除
  audit_status          Int                   @default(1) // 审核状态：1-已审核，0-待审核，-1-审核不通过
  audit_reason          String?               @db.VarChar(255) // 审核不通过原因
  audit_at              BigInt?               // 审核时间
  audit_by              BigInt?               // 审核人ID
  
  // 管理员回复
  admin_reply           String?               @db.Text // 管理员回复
  admin_reply_at        BigInt?               // 管理员回复时间
  admin_reply_by        BigInt?               // 管理员回复人ID
  
  // 统计字段
  helpful_count         Int                   @default(0) // 有用数量
  view_count            Int                   @default(0) // 查看次数
  
  // 商品快照信息（评价时的商品信息）
  product_name_snapshot String?               // 商品名称快照
  product_image_snapshot String?              // 商品图片快照
  sku_specifications_snapshot Json?           // SKU规格快照

  // 评价图片
  image_urls            Json?                 @default("[]") // 评价图片URL列表，JSON数组格式
  
  // 审计字段
  created_at            BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_at            BigInt                @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  deleted_at            BigInt?               // 删除时间（时间戳，毫秒），用于软删除

  // 关联关系
  orders                orders                @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_item_reviews_order_id")
  order_items           order_items           @relation(fields: [order_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_item_reviews_order_item_id")

  // 索引
  @@index([order_id], map: "idx_order_item_reviews_order_id")
  @@index([order_item_id], map: "idx_order_item_reviews_order_item_id")
  @@index([user_id], map: "idx_order_item_reviews_user_id")
  @@index([goods_spu_id], map: "idx_order_item_reviews_goods_spu_id")
  @@index([goods_sku_id], map: "idx_order_item_reviews_goods_sku_id")
  @@index([created_at], map: "idx_order_item_reviews_created_at")
  @@index([status], map: "idx_order_item_reviews_status")
  @@index([audit_status], map: "idx_order_item_reviews_audit_status")
  @@index([deleted_at], map: "idx_order_item_reviews_deleted_at")
  @@index([is_evaluated], map: "idx_order_item_reviews_is_evaluated")

  @@map("order_item_reviews")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_items.prisma
model order_items {
  id                        BigInt   @id @default(autoincrement()) // 订单项ID，主键，自增长
  order_id                  BigInt   // 关联的订单ID，外键
  goods_spu_id              BigInt?  // 商品SPU ID，可能为空（第三方平台商品）
  goods_sku_id              BigInt?  // 商品SKU ID，可能为空（第三方平台商品）
  spu_code_snapshot         String?  @db.VarChar(100) // 下单时SPU编码的快照
  spu_name_snapshot         String?  // 下单时SPU名称的快照
  product_name              String   // 商品名称（显示用）
  sku_code                  String?  @db.VarChar(100) // SKU编码
  sku_specifications        Json?    // SKU规格信息，JSON格式存储，如{"颜色":"红色","尺寸":"XL"}
  product_image             String?  // 商品图片URL
  unit_price                Decimal  @db.Decimal(12, 2) // 单价
  market_price_snapshot     Decimal? @db.Decimal(12, 2) // 下单时市场价的快照
  cost_price_snapshot       Decimal? @db.Decimal(12, 2) // 下单时成本价的快照（内部使用）
  weight_snapshot           Decimal? @db.Decimal(10, 3) // 下单时商品重量的快照，单位：千克
  volume_snapshot           Decimal? @db.Decimal(10, 6) // 下单时商品体积的快照，单位：立方米
  third_party_spu_id        String?  @db.VarChar(255) // 第三方平台SPU ID
  third_party_sku_id        String?  @db.VarChar(255) // 第三方平台SKU ID
  third_party_product_code  String?  @db.VarChar(255) // 第三方平台商品编码
  third_party_item_snapshot Json?    // 第三方平台订单项信息快照，JSON格式
  quantity                  Int      @default(1) // 购买数量
  total_price               Decimal  @db.Decimal(12, 2) // 商品总金额（单价×数量）
  item_paid_amount          Decimal  @db.Decimal(12, 2) // 该订单项实际支付金额（考虑优惠后）
  shipped_quantity          Int      @default(0) // 已发货数量
  created_at                BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_at                BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  orders                    orders    @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_items_order_id") // 关联到订单表
  order_package_items       order_package_items[] // 关联到包裹项
  order_item_reviews        order_item_reviews[] // 关联到商品评价

  @@index([goods_sku_id], map: "idx_order_items_goods_sku_id")
  @@index([order_id], map: "idx_order_items_order_id")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_logs.prisma
model order_logs {
  id            BigInt  @id @default(autoincrement()) // 日志ID，主键，自增长
  order_id      BigInt  // 关联的订单ID，外键
  operator_id   BigInt? // 操作者ID，可能是用户ID或管理员ID
  operator_type Int     @db.SmallInt // 操作者类型：1-系统 2-管理员 3-用户 4-第三方平台
  operator_name String? @db.VarChar(100) // 操作者名称，用于显示
  action        String  @db.VarChar(255) // 操作动作，如创建订单、支付订单、发货、确认收货等
  details       String? // 操作详情，可包含JSON格式的详细信息
  created_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  orders        orders  @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_logs_order_id") // 关联到订单表

  @@index([operator_id], map: "idx_order_logs_operator_id")
  @@index([order_id], map: "idx_order_logs_order_id")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_package_items.prisma
model order_package_items {
  id              BigInt @id @default(autoincrement()) // 包裹项ID
  package_id      BigInt // 关联包裹ID
  order_item_id   BigInt // 关联订单项ID
  quantity        Int    // 该包裹中商品的数量（支持拆分）
  created_at      BigInt @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  updated_at      BigInt @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  order_package   order_packages @relation(fields: [package_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_package_items_package_id")
  order_item      order_items @relation(fields: [order_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_package_items_order_item_id")

  @@index([package_id], map: "idx_order_package_items_package_id")
  @@index([order_item_id], map: "idx_order_package_items_order_item_id")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_packages.prisma
model order_packages {
  id                    BigInt  @id @default(autoincrement()) // 包裹ID
  order_id              BigInt  // 关联订单ID
  package_sn            String  @unique @db.VarChar(64) // 包裹编号
  shipping_company_code String? @db.VarChar(50) // 物流公司编码
  shipping_company_name String? @db.VarChar(100) // 物流公司名称
  tracking_number       String? @db.VarChar(100) // 物流跟踪号
  shipping_method       Int?    // 配送方式
  shipping_status       Int     @default(0) // 包裹状态：0-待发货 1-已发货 2-已收货
  recipient_name        String  @db.VarChar(100) // 收件人姓名
  recipient_phone       String  @db.VarChar(20) // 收件人电话
  region_path_name      String? // 地区完整路径名称
  street_address        String  // 详细街道地址
  postal_code           String? @db.VarChar(20) // 邮政编码
  images_url            Json? // 附件图片URL地址数组
  shipping_origin       String? @db.VarChar(255) // 发货地
  remark                String? // 包裹备注
  is_express_validated  Int     @default(0) // 是否验证快递：0-否 1-是
  created_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  updated_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  shipped_at            BigInt? // 发货时间
  orders                orders  @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_packages_order_id")
  order_package_items   order_package_items[] // 与包裹项的关联
  expressSubscription   expressSubscription[] // 与快递订阅的关联

  @@index([order_id], map: "idx_order_packages_order_id")
  @@index([tracking_number], map: "idx_order_packages_tracking_number")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_remarks.prisma
/// 订单管理员备注表，用于存储订单的富文本备注内容
model order_remarks {
  id         BigInt   @id @default(autoincrement())
  order_id   BigInt
  content    String?  @db.Text
  created_at BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  updated_at BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  created_by BigInt?
  updated_by BigInt?
  deleted_at BigInt?
  orders     orders   @relation(fields: [order_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_order_remarks_order_id")

  @@index([order_id], map: "idx_order_remarks_order_id")
  @@index([created_at], map: "idx_order_remarks_created_at")
  @@index([deleted_at], map: "idx_order_remarks_deleted_at")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_salespeople.prisma
model order_salespeople {
  id             BigInt @id @default(autoincrement()) // 关联ID，主键，自增长
  order_id       BigInt // 订单ID，关联订单表
  salesperson_id BigInt // 中控台业务员ID，关联业务员信息
  created_at     BigInt @default(dbgenerated("(EXTRACT(epoch FROM now()) * (1000)::numeric)")) // 创建时间（时间戳，毫秒）

  @@unique([order_id, salesperson_id], map: "order_salesperson_unique_assignment") // 订单与业务员的关联必须唯一，一个订单只能关联到同一业务员一次
  @@index([order_id], map: "idx_order_salesperson_order_id") // 订单ID索引，提高查询效率
  @@index([salesperson_id], map: "idx_order_salesperson_salesperson_id") // 业务员ID索引，便于根据业务员查询订单
  @@schema("base")
}


// ../apps/master/prisma/models/orders/order_shipping_info.prisma
model order_shipping_info {
  id                    BigInt  @id @default(autoincrement()) // 配送信息ID，主键，自增长
  order_id              BigInt  @unique // 关联的订单ID，外键，一对一关系
  recipient_name        String  @db.VarChar(100) // 收件人姓名
  recipient_phone       String  @db.VarChar(20) // 收件人电话
  region_province_id    Int?    // 省份ID
  region_city_id        Int?    // 城ID
  region_district_id    Int?    // 区/县 ID
  region_path_name      String? // 地区完整路径名称，如“广东省/广州市/天河区”
  street_address        String  // 详细街道地址
  postal_code           String? @db.VarChar(20) // 邮政编码
  shipping_company_code String? @db.VarChar(50) // 物流公司编码
  shipping_company_name String? @db.VarChar(100) // 物流公司名称
  tracking_number       String? @db.VarChar(100) // 物流单号
  created_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戟，毫秒）
  updated_at            BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戟，毫秒）
  orders                orders  @relation(fields: [order_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_order_shipping_info_order_id") // 关联到订单表

  @@index([recipient_phone], map: "idx_order_shipping_info_recipient_phone")
  @@index([tracking_number], map: "idx_order_shipping_info_tracking_number")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/orders.prisma
model orders {
  id                        BigInt                   @id @default(autoincrement()) // 订单ID，主键，自增长
  user_id                   BigInt                   // 用户ID，下单用户
  third_party_platform_id   BigInt?                  // 第三方平台ID（已弃用）
  third_party_order_sn      String?                  @db.VarChar(128) // 第三方订单号，字符串类型
  channel_id                BigInt?                  // 渠道ID，关联渠道管理
  platform_id               BigInt?                  // 平台ID，关联平台管理
  store_id                  BigInt?                  // 店铺ID，关联店铺管理
  purchase_order_number         String?              @db.VarChar(50)    // 采购订单编号，关联csm.purchase_order表
  order_status              Int                      @default(0) // 订单状态：0-待付款 1-已付款待发货 2-已发货待收货 3-交易成功 4-已关闭 5-已退款
  payment_status            Int                      @default(0) // 支付状态：0-未支付 1-部分支付 2-已支付 3-退款中 4-已退款
  shipping_status           Int                      @default(0) // 配送状态：0-未发货 1-已发货 2-部分发货 3-已收货 4-退货中 5-已退货
  send_sync                 Int                      @default(0) // 发货同步状态：0-未同步 1-已同步
  invoice_status            Int                      @default(0) // 开票状态：0-未开票 1-已开票
  total_product_amount      Decimal                  @default(0.00) @db.Decimal(12, 2) // 商品总金额（未计优惠）
  shipping_fee              Decimal                  @default(0.00) @db.Decimal(10, 2) // 运费
  discount_amount           Decimal                  @default(0.00) @db.Decimal(10, 2) // 优惠金额
  tax_amount                Decimal                  @default(0.00) @db.Decimal(10, 2) // 税费
  total_amount              Decimal                  @default(0.00) @db.Decimal(12, 2) // 订单总金额（含运费、优惠等）
  paid_amount               Decimal                  @default(0.00) @db.Decimal(12, 2) // 已支付金额
  payment_method            String?                  @db.VarChar(50) // 支付方式名称
  payment_method_id         Int?                     @default(0) // 支付方式ID：1-微信支付 2-支付宝 3-银联 4-货到付款 5-其他
  payment_sn                String?                  @db.VarChar(128) // 支付流水号
  shipping_method           String?                  @db.VarChar(50) // 配送方式名称
  order_source              Int                      @default(0) // 订单来源：0-系统创建 1-后台创建 2-商城下单 3-APP下单 4-小程序下单
  order_type                Int                      @default(0) // 订单类型：0-普通订单 1-秒杀订单 2-拼团订单 3-预售订单 4-积分订单
  remark                    String?                  // 创建订单时备注
  admin_remark              String?                  // 管理员备注（中控台备注）
  created_at                BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_at                BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  paid_at                   BigInt?                  // 支付时间（时间戳，毫秒）
  shipped_at                BigInt?                  // 发货时间（时间戳，毫秒）
  completed_at              BigInt?                  // 完成时间（时间戳，毫秒）
  cancelled_at              BigInt?                  // 取消时间（时间戳，毫秒）
  cancel_reason             String?                  // 取消原因
  deleted_at                BigInt?                  // 删除时间（时间戳，毫秒，软删除）
  // 第三方平台关联已移除
  channel                   channel?      @relation(fields: [channel_id], references: [id]) // 渠道关联
  // 注意：platform_id 和 store_id 字段不做强关联，只作为外键字段存储
  order_logs                order_logs[]             // 订单日志关联
  order_shipping_info       order_shipping_info?     // 订单配送信息关联
  order_items               order_items[]            // 订单商品项关联
  order_packages            order_packages[]         // 订单包裹关联
  order_followers           order_followers[]        // 订单跟单员关联
  order_remarks             order_remarks[]          // 订单备注关联
  payment_records           payment_records[]        // 支付记录关联
  order_item_reviews        order_item_reviews[]     // 订单商品评价关联
  purchase_orders           purchase_order[]         // 采购订单关联

  @@index([created_at], map: "idx_orders_created_at")
  @@index([deleted_at], map: "idx_orders_deleted_at")
  @@index([third_party_platform_id], map: "idx_orders_third_party_platform_id")
  @@index([channel_id], map: "idx_orders_channel_id")
  @@index([platform_id], map: "idx_orders_platform_id")
  @@index([store_id], map: "idx_orders_store_id")
  @@index([purchase_order_number], map: "idx_orders_purchase_order_number")
  @@schema("base")
}


// ../apps/master/prisma/models/orders/payment_methods.prisma
/// 支付方式表，存储系统支付方式数据
model PaymentMethods {
  id            BigInt   @id @default(autoincrement()) /// 支付方式ID，主键，自增长
  name          String   @db.VarChar(50) /// 支付方式名称
  enabled       Int      @default(1) @db.SmallInt /// 是否启用：0-禁用，1-启用
  created_at    BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间（时间戳，毫秒）
  updated_at    BigInt   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间（时间戳，毫秒）
  deleted_at    BigInt?  /// 删除时间（时间戳，毫秒），软删除标记
  created_by    BigInt?  /// 创建者ID
  updated_by    BigInt?  /// 更新者ID
  // orders        orders[] /// 关联的订单 - 暂时注释掉关联关系

  @@index([deleted_at], map: "idx_payment_methods_deleted_at")
  @@index([enabled], map: "idx_payment_methods_enabled")
  @@map("payment_methods")
  @@schema("base")
}


// ../apps/master/prisma/models/payment/payment_configs.prisma
/// 支付配置表，存储各类支付方式的配置信息
model PaymentConfigs {
  // 主键
  id            BigInt    @id @default(autoincrement()) /// @db.Comment('主键ID，系统自动生成')
  
  // 基本信息
  payment_type  String    @db.VarChar(50)               /// @db.Comment('支付类型：wechat-微信支付，alipay-支付宝，unionpay-银联等')
  config_name   String    @db.VarChar(100)              /// @db.Comment('配置名称，便于管理')
  
  // 状态控制
  enabled       Int       @default(0) @db.SmallInt      /// @db.Comment('是否启用：0-禁用，1-启用')
  is_default    Int       @default(0) @db.SmallInt      /// @db.Comment('是否默认配置：0-否，1-是')
  
  // 配置数据（JSON格式存储）
  config_data   String    @db.Text                      /// @db.Comment('配置数据，JSON格式存储各种配置参数')
  
  // 证书文件存储（Base64编码）
  private_key   String?   @db.Text                      /// @db.Comment('私钥文件内容，Base64编码')
  public_key    String?   @db.Text                      /// @db.Comment('公钥文件内容，Base64编码')
  certificate   String?   @db.Text                      /// @db.Comment('证书文件内容，Base64编码')
  
  // 微信支付专用字段
  wechat_pay_cert String? @db.Text                      /// @db.Comment('微信支付公钥证书，Base64编码')
  
  // 支付宝专用字段
  alipay_public_key String? @db.Text                    /// @db.Comment('支付宝公钥，Base64编码')
  alipay_cert       String? @db.Text                    /// @db.Comment('支付宝根证书，Base64编码')
  
  // 环境配置
  environment   String    @default("sandbox") @db.VarChar(20) /// @db.Comment('环境：sandbox-沙箱，production-生产')
  
  // 回调地址
  notify_url    String?   @db.VarChar(500)              /// @db.Comment('支付回调通知地址')
  
  // 审计字段
  created_by    BigInt?                                 /// @db.Comment('创建者ID')
  updated_by    BigInt?                                 /// @db.Comment('更新者ID')
  created_at    BigInt                                  /// @db.Comment('创建时间戳（毫秒）')
  updated_at    BigInt                                  /// @db.Comment('更新时间戳（毫秒）')
  deleted_at    BigInt?                                 /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark        String?   @db.VarChar(255)              /// @db.Comment('备注信息')

  // 索引
  @@unique([payment_type, config_name])
  @@index([payment_type])
  @@index([enabled])
  @@index([is_default])
  @@map("payment_configs")
  @@schema("base")
}


// ../apps/master/prisma/models/payment/payment_records.prisma
model payment_records {
  // 主键
  id                    BigInt                   @id @default(autoincrement()) // 支付记录ID，主键，自增长

  // 基本信息
  payment_sn            String                   @unique @db.VarChar(64) // 支付流水号，唯一标识
  order_id              BigInt                   // 关联订单ID
  user_id               BigInt                   // 用户ID
  
  // 支付信息
  payment_method_id     Int                      // 支付方式ID：1-微信支付 2-支付宝 3-银联 4-货到付款 5-其他
  payment_method_name   String                   @db.VarChar(50) // 支付方式名称（冗余字段）
  payment_amount        Decimal                  @db.Decimal(12, 2) // 支付金额
  currency              String                   @default("CNY") @db.VarChar(10) // 货币类型，默认人民币
  
  // 第三方支付信息
  third_party_trade_no  String?                  @db.VarChar(128) // 第三方支付流水号（如微信支付单号）
  third_party_app_id    String?                  @db.VarChar(64) // 第三方应用ID
  third_party_mch_id    String?                  @db.VarChar(64) // 第三方商户号
  
  // 支付状态
  payment_status        Int                      @default(0) // 支付状态：0-待支付 1-支付中 2-支付成功 3-支付失败 4-已退款 5-部分退款
  payment_result        String?                  @db.Text // 支付结果详情（JSON格式）
  
  // 时间信息
  payment_time          BigInt?                  // 支付完成时间（时间戳，毫秒）
  expire_time           BigInt?                  // 支付过期时间（时间戳，毫秒）
  notify_time           BigInt?                  // 异步通知时间（时间戳，毫秒）
  
  // 退款信息
  refund_amount         Decimal                  @default(0.00) @db.Decimal(12, 2) // 已退款金额
  refund_reason         String?                  @db.VarChar(255) // 退款原因
  refund_time           BigInt?                  // 退款时间（时间戳，毫秒）
  
  // 通知信息
  notify_url            String?                  @db.VarChar(255) // 异步通知地址
  notify_count          Int                      @default(0) // 通知次数
  notify_status         Int                      @default(0) // 通知状态：0-未通知 1-通知成功 2-通知失败
  
  // 其他信息
  client_ip             String?                  @db.VarChar(45) // 客户端IP地址
  user_agent            String?                  @db.VarChar(500) // 用户代理信息
  description           String?                  @db.VarChar(255) // 支付描述
  attach                String?                  @db.VarChar(255) // 附加数据
  
  // 认款审核字段
  recognition_status    Int                      @default(0) // 认款状态：0-待认款 1-待审核 2-审核通过 3-审核驳回
  audit_attachments     String?                  @db.Text // 审核附件（JSON格式）
  auditor_id            BigInt?                  // 审核人ID
  audit_time            BigInt?                  // 审核时间（时间戳，毫秒）

  // 审计字段
  created_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 创建时间（时间戳，毫秒）
  updated_at            BigInt                   @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) // 更新时间（时间戳，毫秒）
  created_by            BigInt?                  // 创建人ID
  updated_by            BigInt?                  // 更新人ID
  deleted_at            BigInt?                  // 删除时间（时间戳，毫秒，软删除）
  remark                String?                  @db.VarChar(500) // 备注信息

  // 关联关系
  order                 orders                   @relation(fields: [order_id], references: [id]) // 关联订单

  // 索引
  @@index([payment_sn], map: "idx_payment_records_payment_sn")
  @@index([order_id], map: "idx_payment_records_order_id")
  @@index([user_id], map: "idx_payment_records_user_id")
  @@index([payment_method_id], map: "idx_payment_records_payment_method_id")
  @@index([payment_status], map: "idx_payment_records_payment_status")
  @@index([third_party_trade_no], map: "idx_payment_records_third_party_trade_no")
  @@index([payment_time], map: "idx_payment_records_payment_time")
  @@index([recognition_status], map: "idx_payment_records_recognition_status")
  @@index([auditor_id], map: "idx_payment_records_auditor_id")
  @@index([audit_time], map: "idx_payment_records_audit_time")
  @@index([created_at], map: "idx_payment_records_created_at")
  @@index([deleted_at], map: "idx_payment_records_deleted_at")

  @@schema("base")
}


// ../apps/master/prisma/models/platform/channel.prisma
/// 渠道信息表，存储平台渠道数据
model channel {
  /// @db.Comment('渠道ID，16位雪花算法，系统自动生成')
  id              BigInt              @id(map: "channel_pkey")

  // 基本信息
  /// @db.Comment('渠道名称')
  name            String              @db.VarChar(100)
  /// @db.Comment('渠道图标URL')
  icon_url        String?             @db.VarChar(255)
  /// @db.Comment('是否内置渠道，1是0否')
  is_built_in     Int                 @default(0) @db.SmallInt

  // 审计字段
  /// @db.Comment('创建时间')
  created_at      BigInt              @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// @db.Comment('更新时间')
  updated_at      BigInt              @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))
  /// @db.Comment('删除时间，软删除标记')
  deleted_at      BigInt?
  /// @db.Comment('创建人ID')
  created_by      BigInt?
  /// @db.Comment('更新人ID')
  updated_by      BigInt?
  
  // 关联字段
  orders            orders[]            // 关联的订单
  goods_sku_channels GoodsSkuChannel[]  // 关联的商品SKU渠道
  goods_sku_platform_relations GoodsSkuPlatformRelation[]  // 关联的商品SKU平台关系
  delivery_methods  csm_delivery_method[] // 关联的配送方式

  @@index([created_at], map: "idx_channel_management_created_at")
  @@index([deleted_at], map: "idx_channel_management_deleted_at")
  @@index([is_built_in], map: "idx_channel_management_is_built_in")
  @@index([name], map: "idx_channel_management_name")
  @@schema("base")
}


// ../apps/master/prisma/models/platform/platform.prisma
/// 平台信息表，存储销售平台数据
model platform {
  // 主键字段
  id            BigInt   @id /// @db.Comment('平台ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  name          String   @db.VarChar(50) /// @db.Comment('平台名称')
  code          String   @db.VarChar(50) /// @db.Comment('平台代码')
  spider_config Json?    /// @db.Comment('spider配置，JSON格式')
  
  // 状态信息
  status        Int      @default(1) /// @db.Comment('状态：1-启用，0-禁用')
  
  // 关联字段
  channel_id    BigInt   /// @db.Comment('所属渠道ID')
  
  // 审计字段
  created_at    BigInt   /// @db.Comment('创建时间')
  updated_at    BigInt   /// @db.Comment('更新时间')
  deleted_at    BigInt?  /// @db.Comment('删除时间，软删除标记')
  created_by    BigInt?  /// @db.Comment('创建人ID')
  updated_by    BigInt?  /// @db.Comment('更新人ID')

  // 关联字段
  goods_sku_platform_relations GoodsSkuPlatformRelation[]  // 关联的商品SKU平台关系

  // 索引
  @@index([name], name: "idx_platform_name")
  @@index([code], name: "idx_platform_code")
  @@index([channel_id], name: "idx_platform_channel_id")
  @@index([status], name: "idx_platform_status")
  @@schema("base")
}


// ../apps/master/prisma/models/platform/report.prisma
/// 报备记录表，存储客户报备信息
model report {
  // 主键字段
  id                BigInt   @id /// @db.Comment('报备记录ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  report_number     String   @db.VarChar(50) /// @db.Comment('报备单号，系统自动生成，格式：RB+年月日+6位序号')
  customer_name     String   @db.VarChar(100) /// @db.Comment('客户姓名')
  customer_phone    String   @db.VarChar(20) /// @db.Comment('客户电话')
  customer_group    String   @db.VarChar(50) /// @db.Comment('客户组，如：VIP客户、普通客户等')
  
  // 报备信息
  platform_id       BigInt   /// @db.Comment('报备平台ID，关联平台表')
  channel_id        BigInt   /// @db.Comment('报备渠道ID，关联渠道表')
  report_type       String   @db.VarChar(20) /// @db.Comment('报备类型，如：新客户、老客户回访等')
  report_content    String?  @db.Text /// @db.Comment('报备内容描述')
  
  // 状态信息
  status            Int      @default(1) /// @db.Comment('状态：1-待处理，2-处理中，3-已完成，4-已取消')
  priority          Int      @default(2) /// @db.Comment('优先级：1-高，2-中，3-低')
  
  // 处理信息
  assigned_to       BigInt?  /// @db.Comment('分配给的用户ID')
  processed_at      BigInt?  /// @db.Comment('处理时间戳（毫秒）')
  completed_at      BigInt?  /// @db.Comment('完成时间戳（毫秒）')
  process_notes     String?  @db.Text /// @db.Comment('处理备注')
  
  // 平台集成数据
  platform_data     Json?    /// @db.Comment('平台集成数据，JSON格式存储')
  external_id       String?  @db.VarChar(100) /// @db.Comment('外部系统ID')
  
  // 审计字段
  created_at        BigInt   /// @db.Comment('创建时间戳（毫秒）')
  updated_at        BigInt   /// @db.Comment('更新时间戳（毫秒）')
  deleted_at        BigInt?  /// @db.Comment('删除时间戳（毫秒），软删除标记')
  created_by        BigInt?  /// @db.Comment('创建人ID')
  updated_by        BigInt?  /// @db.Comment('更新人ID')
  
  // 备注字段
  remark            String?  @db.Text /// @db.Comment('备注信息')

  // 索引
  @@index([report_number])
  @@index([customer_phone])
  @@index([platform_id])
  @@index([channel_id])
  @@index([status])
  @@index([created_at])
  @@index([assigned_to])
  @@map("report")
  @@schema("base")
}


// ../apps/master/prisma/models/platform/store.prisma
/// 店铺信息表，存储店铺数据
model store {
  // 主键字段
  id              BigInt   @id /// @db.Comment('店铺ID，16位雪花算法，系统自动生成')

  // 基本信息
  name            String   @db.VarChar(100) /// @db.Comment('店铺名称')
  code            String   @db.VarChar(50) /// @db.Comment('店铺代码')
  account_config  Json?    @default("{}") /// @db.Comment('账户配置，JSON格式')
  cookies         String?  @db.Text /// @db.Comment('登录cookies信息')
  remark          String?  @db.VarChar(255) /// @db.Comment('备注信息')
  principal_name  String?  @db.VarChar(255) /// @db.Comment('负责人姓名')
  invoice_title   String?  @db.VarChar(255) /// @db.Comment('发票抬头')

  // 状态信息
  status          Int      @default(1) /// @db.Comment('状态：1-启用，0-禁用')
  is_auto_sync    Int      @default(0) @db.SmallInt /// @db.Comment('是否自动同步：1-是，0-否')
  cookies_valid   Int?     /// @db.Comment('Cookies有效性：1-有效，0-无效，null-未检测')

  // 关联字段
  platform_id     BigInt   /// @db.Comment('所属平台ID')

  // 审计字段
  created_at      BigInt   /// @db.Comment('创建时间')
  updated_at      BigInt   /// @db.Comment('更新时间')
  deleted_at      BigInt?  /// @db.Comment('删除时间，软删除标记')
  created_by      BigInt?  /// @db.Comment('创建人ID')
  updated_by      BigInt?  /// @db.Comment('更新人ID')

  // 关联字段
  goods_sku_platform_relations GoodsSkuPlatformRelation[]  // 关联的商品SKU平台关系

  // 索引
  @@index([name], name: "idx_store_name")
  @@index([code], name: "idx_store_code")
  @@index([platform_id], name: "idx_store_platform_id")
  @@index([status], name: "idx_store_status")
  @@index([cookies_valid], name: "idx_store_cookies_valid")
  @@schema("base")
}


// ../apps/master/prisma/models/region.prisma
model Region {
  id             Int     @id
  citycode       Int?    @default(0) @map("citycode")
  cityname       String? @default("") @map("cityname") @db.VarChar(32)
  cityfullname   String? @default("") @map("cityfullname") @db.VarChar(500)
  citycodeprefix Int?    @default(0) @map("citycodeprefix")
  level          Int?    @default(0) @map("level") @db.SmallInt
  parent_id      Int?    @default(0) @map("parent_id")
  out_field      Int?    @default(0) @map("out_field") @db.SmallInt

  @@map("region")
  @@schema("base")
  @@index([citycode], name: "idx_region_citycode")
  @@index([level], name: "idx_region_level")
  @@index([parent_id], name: "idx_region_parent_id")
}


// ../apps/master/prisma/models/self_brand/self_brand_empower.prisma
// 自主品牌授权模型
model SelfBrandEmpower {
  id                  BigInt    @id @default(autoincrement())         // 主键ID
  empower_number      String    @unique @db.VarChar(100)              // 授权编号
  empower_unit        String    @db.VarChar(255)                      // 授权单位
  empower_this_unit   String    @db.VarChar(255)                      // 兹授权单位
  empower_brand       String    @db.VarChar(100)                      // 授权品牌
  empower_image       String    @db.VarChar(255)                      // 授权品牌图片
  empower_er_image    String    @db.VarChar(255)                      // 授权品牌二维码图片
  type                Int       @default(0)                           // 商标类型
  empower_start_date  BigInt?                                         // 授权开始日期（毫秒时间戳）
  empower_end_date    BigInt?                                         // 授权结束日期（毫秒时间戳）
  status              Int       @default(1)                           // 状态
  created_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at          BigInt?                                         // 删除时间戳（毫秒，软删除）

  @@index([empower_brand], map: "idx_self_brand_empower_brand")
  @@index([deleted_at], map: "idx_self_brand_empower_deleted_at")
  @@map("self_brand_empowers")
  @@schema("base")
}


// ../apps/master/prisma/models/self_brand/self_brand_rule_code.prisma
// 自主品牌规则码模型
model SelfBrandRuleCode {
  id           BigInt  @id @default(autoincrement())                 // 主键ID
  name         String  @unique @db.VarChar(100)                      // 品牌名称
  rule_code    String  @db.VarChar(16)                               // 溯源码规则
  brand_source String  @db.VarChar(100)                              // 品牌所属地
  created_at   BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at   BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at   BigInt?                                               // 删除时间戳（毫秒，软删除）

  @@index([name], map: "idx_self_brand_rule_code_name")
  @@index([deleted_at], map: "idx_self_brand_rule_code_deleted_at")
  @@map("self_brand_rule_codes")
  @@schema("base")
}


// ../apps/master/prisma/models/self_brand/self_owned_brands.prisma
// 自主品牌模型
model SelfOwnedBrand {
  id                   BigInt    @id @default(autoincrement())         // 主键ID
  name                 String    @db.VarChar(100)                      // 商标名称
  image                String    @db.VarChar(255)                      // logo图片url
  owner_name           String    @db.VarChar(255)                      // 申请人
  owner_address        String    @db.VarChar(255)                      // 申请人地址
  agency               String    @db.VarChar(255)                      // 代理组织机构
  register_id          BigInt?                                         // 注册id
  registration_type    String    @db.VarChar(30)                       // 商标类型
  pre_ann_date         BigInt?                                         // 初审公告日期（毫秒时间戳）
  apply_date           BigInt?                                         // 申请日期（毫秒时间戳）
  reg_ann_date         BigInt?                                         // 注册公告日期（毫秒时间戳）
  last_procedure_status String    @db.VarChar(30)                      // 当前状态
  product_desc         String?   @db.Text                              // 商品服务列表
  exclusive_date_limit String    @db.VarChar(60)                       // 专用权期限
  issues               String?   @db.Text                              // 商标公告
  status               Int       @default(1)                           // 是否使用：1-使用，0-不使用
  class_id             Int       @default(0)                           // 商标类型id
  class_name           String    @db.VarChar(60)                       // 商标类名
  trademark_code       String    @db.VarChar(100)                      // 商标代码
  source_code_rule     String?   @db.VarChar(100)                      // 溯源码规则
  brand_location       String?   @db.VarChar(100)                      // 品牌位置
  created_at           BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at           BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at           BigInt?                                         // 删除时间戳（毫秒，软删除）
  created_by           BigInt?                                         // 创建人ID
  updated_by           BigInt?                                         // 更新人ID

  // 关联
  brand_images         SelfOwnedBrandImage[]
  source_codes         SelfSourceCode[]

  @@index([register_id], map: "idx_self_owned_brands_register_id")
  @@index([apply_date], map: "idx_self_owned_brands_apply_date")
  @@index([created_at], map: "idx_self_owned_brands_created_at")
  @@index([deleted_at], map: "idx_self_owned_brands_deleted_at")
  @@index([created_by], map: "idx_self_owned_brands_created_by")
  @@index([updated_by], map: "idx_self_owned_brands_updated_by")
  @@map("self_owned_brands")
  @@schema("base")
}

// 自主品牌附件模型
model SelfOwnedBrandImage {
  id               BigInt  @id @default(autoincrement())              // 主键ID
  owned_brand_id   BigInt                                             // 自主品牌id
  brand_image      String  @db.VarChar(255)                           // 品牌图文件地址
  created_at       BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at       BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at       BigInt?                                            // 删除时间戳（毫秒，软删除）

  // 关联
  brand            SelfOwnedBrand @relation(fields: [owned_brand_id], references: [id], onDelete: Cascade)

  @@index([owned_brand_id], map: "idx_self_owned_brand_images_brand_id")
  @@index([deleted_at], map: "idx_self_owned_brand_images_deleted_at")
  @@map("self_owned_brand_images")
  @@schema("base")
}


// ../apps/master/prisma/models/self_brand/self_product_brand.prisma
// 产品品牌模型
model SelfProductBrand {
  id             BigInt  @id @default(autoincrement())                // 主键ID
  brand_code     String  @db.VarChar(32)                             // 品牌代码
  brand_name     String  @db.VarChar(255)                            // 品牌名称
  is_self_brand  Int     @default(1)                                 // 是否自主品牌 1否 2是
  created_at     BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at     BigInt  @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at     BigInt?                                             // 删除时间戳（毫秒，软删除）
  created_by     BigInt?                                             // 创建人ID
  updated_by     BigInt?                                             // 更新人ID
  
  // 关联
  // 暂时注释掉未定义的资质关联，避免Prisma验证错误
  // qualifications SelfProductBrandQualification[]

  @@index([brand_code], map: "idx_self_product_brand_code")
  @@index([brand_name], map: "idx_self_product_brand_name") 
  @@index([deleted_at], map: "idx_self_product_brand_deleted_at")
  @@index([created_by], map: "idx_self_product_brands_created_by")
  @@index([updated_by], map: "idx_self_product_brands_updated_by")
  @@map("self_product_brands")
  @@schema("base")
}


// ../apps/master/prisma/models/self_brand/self_source_code_query_log.prisma
// 溯源码查询日志模型
model SelfSourceCodeQueryLog {
  id               BigInt    @id                                            // 主键ID
  source_code_id   BigInt                                                   // 关联的溯源码ID
  source_code      String    @db.VarChar(50)                                // 溯源码
  ip               String    @db.VarChar(50)                                // 查询IP
  location         String?   @db.VarChar(255)                               // 查询地址
  query_time       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 查询时间戳（毫秒）
  created_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at       BigInt?                                                  // 删除时间戳（毫秒，软删除）

  // 关联
  sourceCode       SelfSourceCode  @relation(fields: [source_code_id], references: [id])

  @@index([source_code_id], map: "idx_self_source_code_query_log_source_code_id")
  @@index([source_code], map: "idx_self_source_code_query_log_source_code")
  @@index([query_time], map: "idx_self_source_code_query_log_query_time")
  @@index([deleted_at], map: "idx_self_source_code_query_log_deleted_at")
  @@map("self_source_code_query_logs")
  @@schema("base")
}


// ../apps/master/prisma/models/self_brand/self_source_code.prisma
// 自主品牌溯源码模型
model SelfSourceCode {
  id               BigInt    @id @default(autoincrement())                 // 主键ID
  brand_id         BigInt                                                  // 关联的自主品牌ID
  source_code      String    @unique @db.VarChar(50)                       // 溯源码
  status           Int       @default(0)                                   // 状态：0-未启用，1-已启用
  activation_time  BigInt?                                                 // 激活时间戳（毫秒）
  first_query_time BigInt?                                                 // 首次查询时间戳（毫秒）
  query_count      Int       @default(0)                                   // 查询次数
  created_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at       BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at       BigInt?                                                 // 删除时间戳（毫秒，软删除）
  created_by       BigInt?                                                 // 创建人ID
  updated_by       BigInt?                                                 // 更新人ID

  // 关联
  brand            SelfOwnedBrand  @relation(fields: [brand_id], references: [id])
  queryLogs        SelfSourceCodeQueryLog[]                               // 查询日志

  @@index([brand_id], map: "idx_self_source_code_brand_id")
  @@index([source_code], map: "idx_self_source_code_source_code") 
  @@index([status], map: "idx_self_source_code_status")
  @@index([deleted_at], map: "idx_self_source_code_deleted_at")
  @@index([created_by], map: "idx_self_source_codes_created_by")
  @@index([updated_by], map: "idx_self_source_codes_updated_by")
  @@map("self_source_codes")
  @@schema("base")
}


// ../apps/master/prisma/models/system/express_track.prisma
/// 快递物流记录表
model ExpressTrackSystem {
  // 主键
  id                  BigInt    @id(map: "express_track_system_pkey") @default(autoincrement()) /// @db.Comment('记录ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  shipping_company_code  String    @db.VarChar(50) @map("express_code") /// @db.Comment('快递公司编码')
  express_name        String    @db.VarChar(100)              /// @db.Comment('快递公司名称')
  tracking_number     String    @db.VarChar(100) @unique @map("express_no") /// @db.Comment('快递单号')
  
  // 物流状态
  status              Int       @default(0)                   /// @db.Comment('物流状态：0-在途中，1-揽收，2-疑难，3-已签收，4-退签，5-派件，6-退回，8-清关')
  track_data          Json?                                   /// @db.Comment('物流轨迹数据')
  last_track          String?   @db.Text                      /// @db.Comment('最新物流信息')
  is_subscribed       Boolean   @default(false)               /// @db.Comment('是否已订阅')
  sub_status          Int?                                    /// @db.Comment('订阅状态')
  sub_biz_id          String?   @db.VarChar(100)              /// @db.Comment('订阅业务ID')
  
  // 审计字段
  created_by          BigInt                                  /// @db.Comment('创建人ID')
  created_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000")) /// @db.Comment('创建时间戳（毫秒）')
  updated_by          BigInt?                                 /// @db.Comment('更新人ID')
  updated_at          BigInt?                                 /// @db.Comment('更新时间戳（毫秒）')
  deleted_at          BigInt?                                 /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  
  @@index([tracking_number], map: "idx_express_track_system_tracking_number")
  @@map("express_track_system")
  @@schema("base")
}


// ../apps/master/prisma/models/system/firewall_rules.prisma
/// 防火墙IP规则表，存储IP访问控制规则
model BaseFirewallRule {
  // 主键
  id            BigInt    @id @default(autoincrement()) /// @db.Comment('主键ID，系统自动生成')
  
  // 规则信息
  rule_name     String    @db.VarChar(100)              /// @db.Comment('规则名称')
  ip_address    String    @db.VarChar(45)               /// @db.Comment('IP地址，支持IPv4和IPv6')
  ip_mask       String?   @db.VarChar(45)               /// @db.Comment('子网掩码，用于IP段匹配')
  rule_type     Int       @default(1) @db.SmallInt      /// @db.Comment('规则类型：1-黑名单(禁止)，2-白名单(允许)')
  match_type    Int       @default(1) @db.SmallInt      /// @db.Comment('匹配类型：1-精确匹配，2-IP段匹配，3-通配符匹配')
  
  // 状态和配置
  status        Int       @default(1) @db.SmallInt      /// @db.Comment('状态：0-禁用，1-启用')
  priority      Int       @default(0) @db.SmallInt      /// @db.Comment('优先级，数值越大优先级越高')
  
  // 统计信息
  hit_count     BigInt    @default(0)                   /// @db.Comment('命中次数')
  last_hit_at   BigInt?                                 /// @db.Comment('最后命中时间戳（毫秒）')
  
  // 审计字段
  created_by    BigInt?                                 /// @db.Comment('创建者ID')
  updated_by    BigInt?                                 /// @db.Comment('更新者ID')
  created_at    BigInt                                  /// @db.Comment('创建时间戳（毫秒）')
  updated_at    BigInt                                  /// @db.Comment('更新时间戳（毫秒）')
  deleted_at    BigInt?                                 /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark        String?   @db.VarChar(255)              /// @db.Comment('备注信息')

  @@index([ip_address])
  @@index([rule_type])
  @@index([status])
  @@index([priority])
  @@map("firewall_rules")
  @@schema("base")
}


// ../apps/master/prisma/models/system/message_board.prisma
// 留言板模型
model MessageBoard {
  id          BigInt   @id @default(autoincrement())
  current_url String   @db.VarChar(255)               // 当前链接
  submitter   String   @db.VarChar(100)               // 提交人
  content     String   @db.Text                       // 内容（富文本）
  created_at  BigInt   @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at  BigInt   @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at  BigInt?                                 // 删除时间戳（毫秒，软删除）
  created_by  BigInt?                                 // 创建人ID
  updated_by  BigInt?                                 // 更新人ID
  remark      String?  @db.Text                       // 备注

  @@index([deleted_at], map: "idx_message_board_deleted_at")
  @@index([created_at], map: "idx_message_board_created_at")
  @@index([submitter], map: "idx_message_board_submitter")
  @@map("message_board")
  @@schema("base")
}


// ../apps/master/prisma/models/system/role_dept.prisma
/// 角色与部门多对多关联表，用于数据权限控制，无需单独主键和更新时间
model role_dept {
  role_id    BigInt  /// @db.Comment('角色ID')
  dept_id    BigInt  /// @db.Comment('部门ID')
  created_at BigInt  /// @db.Comment('创建时间戳')

  @@id([role_id, dept_id]) // 联合主键，防止重复关联
  @@index([role_id])
  @@index([dept_id])
  @@map("role_dept")
  @@schema("base")
}


// ../apps/master/prisma/models/system/role_menu.prisma
/// 角色与菜单多对多关联表，仅用于权限分配，无需单独主键和更新时间
model role_menu {
  role_id   BigInt  /// @db.Comment('角色ID')
  menu_id   BigInt  /// @db.Comment('菜单ID')
  created_at BigInt /// @db.Comment('创建时间戳')

  @@id([role_id, menu_id]) // 联合主键，防止重复关联
  @@index([role_id])
  @@index([menu_id])
  @@map("role_menu")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_config.prisma
/// 系统配置表，存储各类系统配置信息
model BaseSystemConfig {
  // 主键
  id            BigInt    @id @default(autoincrement()) /// @db.Comment('主键ID，系统自动生成')
  
  // 基本信息
  config_type   String    @db.VarChar(50)               /// @db.Comment('配置类型：oss-存储配置，sms-短信配置，wechat-微信配置，email-邮件配置等')
  config_key    String    @db.VarChar(100)              /// @db.Comment('配置键')
  config_value  String    @db.Text                      /// @db.Comment('配置值，可存储JSON字符串')
  name          String    @db.VarChar(100)              /// @db.Comment('配置名称，便于管理')
  
  // 系统信息
  sort          Int       @default(0) @db.SmallInt      /// @db.Comment('排序，值越小越靠前')
  is_system     Int       @default(0) @db.SmallInt      /// @db.Comment('是否系统内置：0-否，1-是')
  is_default    Int       @default(0) @db.SmallInt      /// @db.Comment('是否默认配置：0-否，1-是')
  
  // 审计字段
  created_by    BigInt?                                 /// @db.Comment('创建者ID')
  updated_by    BigInt?                                 /// @db.Comment('更新者ID')
  created_at    BigInt                                  /// @db.Comment('创建时间戳（毫秒）')
  updated_at    BigInt                                  /// @db.Comment('更新时间戳（毫秒）')
  deleted_at    BigInt?                                 /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark        String?   @db.VarChar(255)              /// @db.Comment('备注信息')

  @@unique([config_type, config_key])
  @@index([config_type])
  @@map("system_config")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_dept.prisma
/// 部门信息表，存储组织架构信息
model BaseSystemDept {
  // 主键
  id         BigInt  @id @default(autoincrement()) /// @db.Comment('主键，系统自动生成')

  // 基本信息
  name       String  /// @db.Comment('部门名称，必填')
  leader     String? /// @db.Comment('负责人')
  phone      String? /// @db.Comment('联系电话，11位手机号')

  // 状态信息
  status     Int?    @default(1) /// @db.Comment('状态：1-正常，2-停用')
  sort       Int?    @default(0) /// @db.Comment('排序，值越小越靠前')

  // 审计字段
  created_by BigInt? /// @db.Comment('创建者ID')
  updated_by BigInt? /// @db.Comment('更新者ID')
  created_at BigInt? /// @db.Comment('创建时间戳（毫秒）')
  updated_at BigInt? /// @db.Comment('更新时间戳（毫秒）')
  deleted_at BigInt? /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark     String? /// @db.Comment('备注信息')

  @@map("system_dept")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_file.prisma
/// 系统文件表，存储上传的文件信息
model SystemFile {
  // 主键
  id            BigInt    @id                           /// @db.Comment('文件ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  file_name     String    @db.VarChar(255) /// @db.Comment('文件名称，系统生成的唯一文件名')
  original_name String    @db.VarChar(255) /// @db.Comment('原始文件名，用户上传时的文件名')
  file_path     String    /// @db.Comment('文件路径，存储系统中的相对路径')
  file_url      String    /// @db.Comment('文件访问URL，完整的访问地址')
  file_size     BigInt    /// @db.Comment('文件大小，单位字节')
  file_type     String    @db.VarChar(100) /// @db.Comment('文件MIME类型，如image/jpeg、application/pdf等')
  extension     String    @db.VarChar(20)  /// @db.Comment('文件扩展名，不含点，如jpg、pdf等')
  
  // 存储信息
  storage_type  String    @db.VarChar(50) @default("aliyun_oss") /// @db.Comment('存储类型：aliyun_oss-阿里云对象存储，local-本地存储')
  bucket        String?   @db.VarChar(100) /// @db.Comment('存储桶名称，对象存储专用')
  md5           String?   @db.VarChar(32)  /// @db.Comment('文件MD5值，用于文件去重')
  
  // 状态信息
  status        Int       @default(1) /// @db.Comment('状态：1-正常，0-禁用')
  is_public     Boolean   @default(true) /// @db.Comment('是否公开访问：true-公开，false-私有')
  
  // 业务信息
  module        String?   @db.VarChar(50) /// @db.Comment('所属模块，如user、product等')
  biz_type      String?   @db.VarChar(50) /// @db.Comment('业务类型，如avatar、cover等')
  biz_id        BigInt?   /// @db.Comment('业务ID，关联的业务数据ID')
  tags          String?   /// @db.Comment('标签，多个用逗号分隔')
  remark        String?   /// @db.Comment('备注说明')
  
  // 审计字段
  created_by    BigInt    /// @db.Comment('创建人ID，关联用户表')
  created_at    BigInt    @default(dbgenerated("extract(epoch from now()) * 1000")) /// @db.Comment('创建时间戳（毫秒）')
  updated_by    BigInt?   /// @db.Comment('更新人ID，关联用户表')
  updated_at    BigInt?   /// @db.Comment('更新时间戳（毫秒）')
  deleted_by    BigInt?   /// @db.Comment('删除人ID，关联用户表')
  deleted_at    BigInt?   /// @db.Comment('删除时间戳（毫秒），用于软删除')
  
  // 索引
  @@index([created_by], map: "idx_system_file_created_by")
  @@index([file_type], map: "idx_system_file_type")
  @@index([module, biz_type, biz_id], map: "idx_system_file_biz")
  @@index([md5], map: "idx_system_file_md5")
  
  // 表名映射
  @@map("system_file")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_menu.prisma
/// 菜单信息表
model BaseSystemMenu {
  // 主键
  id            BigInt    @id @default(autoincrement())  /// 主键
  
  // 菜单基本信息
  parent_id     BigInt                                  /// 父ID
  level         String    @db.VarChar(500)              /// 组级集合
  name          String    @db.VarChar(50)               /// 菜单名称
  code          String    @db.VarChar(100)              /// 菜单标识代码
  icon          String?   @db.VarChar(50)               /// 菜单图标
  route         String?   @db.VarChar(200)              /// 路由地址
  component     String?   @db.VarChar(255)              /// 组件路径
  redirect      String?   @db.VarChar(255)              /// 跳转地址
  
  // 菜单配置
  is_hidden     Int       @default(1)                   /// 是否隐藏 (1是 0否)
  type          String    @db.VarChar(1)                /// 菜单类型 (M菜单 B按钮 L链接 I iframe)
  status        Int?      @default(0)                   /// 状态 (0正常 1停用)
  sort          Int?      @default(0)                   /// 排序
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID
  updated_by    BigInt?                                 /// 更新者ID
  created_at    BigInt                                  /// 创建时间戳
  updated_at    BigInt                                  /// 更新时间戳
  deleted_at    BigInt?                                 /// 删除时间戳
  
  // 其他信息
  remark        String?   @db.VarChar(255)              /// 备注

  @@map("system_menu")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_operation_log.prisma
/// 系统操作日志表，记录用户操作
model SystemOperationLog {
  // 主键
  id            BigInt    @id                           /// @db.Comment('日志ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  user_id       BigInt                                  /// @db.Comment('操作用户ID')
  username      String    @db.VarChar(50)               /// @db.Comment('操作用户名')
  module        String    @db.VarChar(50)               /// @db.Comment('操作模块')
  operation     String    @db.VarChar(100)              /// @db.Comment('操作类型')
  method        String    @db.VarChar(10)               /// @db.Comment('请求方法')
  path          String    @db.VarChar(255)              /// @db.Comment('请求路径')
  
  // 详细信息
  ip            String    @db.VarChar(50)               /// @db.Comment('操作IP地址')
  user_agent    String?   @db.VarChar(500)              /// @db.Comment('用户代理信息')
  client        String?   @db.VarChar(50)               /// @db.Comment('客户端类型，如中控端、商户端等')
  request_data  Json?                                   /// @db.Comment('请求参数')
  response_data Json?                                   /// @db.Comment('响应数据')
  
  // 状态信息
  status        Int       @default(1)                   /// @db.Comment('状态：1-成功，0-失败')
  error_message String?                                 /// @db.Comment('错误信息')
  execution_time Int?                                   /// @db.Comment('执行时间(毫秒)')
  
  // 审计字段
  created_at    BigInt    @default(dbgenerated("extract(epoch from now()) * 1000")) /// @db.Comment('创建时间戳（毫秒）')
  
  // 索引
  @@index([user_id], map: "idx_system_operation_log_user_id")
  @@index([module, operation], map: "idx_system_operation_log_module_operation")
  @@index([created_at], map: "idx_system_operation_log_created_at")
  
  // 表名映射
  @@map("system_operation_log")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_role.prisma
/// 角色信息表
model BaseSystemRole {
  // 主键
  id          BigInt    @id                           /// 角色ID（雪花算法）

  // 基本信息
  name        String    @db.VarChar(30)               /// 角色名称
  data_scope  Int       @default(1) @db.SmallInt      /// 数据范围：1-全部数据权限 2-自定义数据权限 3-本部门数据权限 4-本人数据权限
  status      Int       @default(1) @db.SmallInt      /// 状态：1-正常 2-停用
  sort        Int       @default(0) @db.SmallInt      /// 排序
  remark      String?   @db.VarChar(255)              /// 备注

  // 审计字段
  created_by  BigInt?                                 /// 创建者ID
  updated_by  BigInt?                                 /// 更新者ID
  created_at  BigInt                                  /// 创建时间戳（毫秒）
  updated_at  BigInt                                  /// 更新时间戳（毫秒）
  deleted_at  BigInt?                                 /// 删除时间戳（毫秒）

  @@map("system_role")
  @@schema("base")
}


// ../apps/master/prisma/models/system/system_user.prisma
/// 用户信息表，存储系统用户数据
model BaseSystemUser {
  // 主键
  id            BigInt    @id                           /// 用户ID，16位雪花算法，系统自动生成
  
  // 基本信息
  user_type     Int       @default(60)                  /// 用户类型：100-主账户，60-子账号，默认子账号
  username      String    @unique                       /// 用户名，必填，唯一，用于登录
  password      String                                  /// 密码，必填，加密存储
  nickname      String?                                 /// 用户昵称，选填，用于显示
  phone         String?   @db.VarChar(11)               /// 手机号，11位数字，可用于登录
  email         String?   @unique                       /// 邮箱，唯一，可用于找回密码
  avatar        String?                                 /// 头像地址，存储图片URL
  
  // 组织架构
  dept_id       BigInt?                                 /// 部门ID，关联 Department 表的 id 字段
  role_id       BigInt?                                 /// 角色ID，关联 Role 表的 id 字段
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：0-正常，1-禁用
  login_ip      String?   @db.VarChar(45)               /// 最后登录IP
  login_time    BigInt?                                 /// 最后登录时间戳（毫秒）
  login_token   String?                                 /// 登录token
  
  // 其他信息
  remark        String?                                 /// 备注信息
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）

  @@map("system_user")
  @@schema("base")
}


// ../apps/official/prisma/models/analytics/active_visitors.prisma
// 实时活跃访客表 (用于15分钟活跃访客统计)
model ActiveVisitor {
  id              String   @id @default(cuid())
  visitor_id      String   @unique // 访客唯一标识
  session_id      String   // 会话ID
  ip_address      String   // IP地址
  last_activity   DateTime @default(now()) // 最后活动时间
  current_page    String?  // 当前页面
  user_agent      String?  // 用户代理
  device_type     String?  // 设备类型
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("active_visitors")
  @@schema("official")
  @@index([visitor_id])
  @@index([session_id])
  @@index([last_activity])
  @@index([ip_address])
}


// ../apps/official/prisma/models/analytics/daily_statistics.prisma
// 每日统计汇总表 (用于提高查询性能)
model DailyStatistic {
  id                String   @id @default(cuid())
  date              DateTime @unique @db.Date // 统计日期
  total_visits      Int      @default(0) // 总访问次数
  unique_visitors   Int      @default(0) // 独立访客数(UV)
  page_views        Int      @default(0) // 页面浏览量(PV)
  new_visitors      Int      @default(0) // 新访客数
  returning_visitors Int     @default(0) // 回访客数
  bounce_rate       Float    @default(0) // 跳出率
  avg_session_duration Float @default(0) // 平均会话时长(秒)
  avg_page_views    Float    @default(0) // 平均页面浏览数
  unique_ips        Int      @default(0) // 独立IP数
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  @@map("daily_statistics")
  @@schema("official")
  @@index([date])
}


// ../apps/official/prisma/models/analytics/page_views.prisma
// 页面浏览记录表
model PageView {
  id              String   @id @default(cuid())
  visit_id        String   // 关联访问记录ID
  visitor_id      String   // 访客ID
  session_id      String   // 会话ID
  ip_address      String   // 访客IP
  page_url        String   // 页面URL
  page_title      String?  // 页面标题
  page_path       String   // 页面路径
  referrer_url    String?  // 上一页URL
  view_time       DateTime @default(now()) // 浏览时间
  duration_seconds Int?    // 在页面停留时长(秒)
  scroll_depth    Float?   // 滚动深度百分比
  is_exit_page    Boolean  @default(false) // 是否退出页面
  created_at      DateTime @default(now())

  // 关联访问记录
  visit WebsiteVisit @relation(fields: [visit_id], references: [id], onDelete: Cascade)

  @@map("page_views")
  @@schema("official")
  @@index([visit_id])
  @@index([visitor_id])
  @@index([session_id])
  @@index([page_url])
  @@index([page_path])
  @@index([view_time])
  @@index([created_at])
}


// ../apps/official/prisma/models/analytics/website_visits.prisma
// 网站访问记录表
model WebsiteVisit {
  id                String   @id @default(cuid())
  visitor_id        String   // 访客唯一标识 (基于IP+User-Agent生成)
  session_id        String   // 会话ID
  ip_address        String   // 访客IP地址
  user_agent        String?  // 用户代理字符串
  device_type       String?  // 设备类型 (mobile, desktop, tablet)
  device_name       String?  // 设备名称 (iPhone, Android, Windows等)
  browser_name      String?  // 浏览器名称
  browser_version   String?  // 浏览器版本
  os_name           String?  // 操作系统名称
  os_version        String?  // 操作系统版本
  country           String?  // 国家
  region            String?  // 省份/地区
  city              String?  // 城市
  referrer_url      String?  // 来源URL
  referrer_domain   String?  // 来源域名
  entry_page        String   // 入口页面
  is_new_visitor    Boolean  @default(true) // 是否新访客
  visit_start_time  DateTime @default(now()) // 访问开始时间
  visit_end_time    DateTime? // 访问结束时间
  page_views        Int      @default(1) // 本次访问的页面浏览数
  duration_seconds  Int?     // 访问时长(秒)
  is_bounce         Boolean  @default(false) // 是否跳出(只访问一个页面)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // 关联页面浏览记录
  page_views_records PageView[]

  @@map("website_visits")
  @@schema("official")
  @@index([visitor_id])
  @@index([session_id])
  @@index([ip_address])
  @@index([visit_start_time])
  @@index([is_new_visitor])
  @@index([created_at])
}


// ../apps/official/prisma/models/base.prisma





// ../apps/official/prisma/models/case.prisma
/// 官方案例表，存储平台案例展示数据
model Case {
  // 主键
  id            BigInt    @id                                /// @db.Comment('案例ID')
  
  // 基本信息
  title         String    @db.VarChar(100)                   /// @db.Comment('案例标题')
  description   String?   @db.VarChar(500)                   /// @db.Comment('案例简介')
  content       String    @db.Text                           /// @db.Comment('案例内容')
  icon          String?   @db.VarChar(100)                   /// @db.Comment('案例图标')
  cover_image   String?   @db.VarChar(255)                   /// @db.Comment('封面图片URL')
  
  // 状态信息
  status        Int       @default(1)                        /// @db.Comment('案例状态：0-隐藏，1-显示')
  view_count    Int       @default(0)                        /// @db.Comment('浏览次数')
  
  // 审计字段
  created_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('创建时间')
  updated_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('更新时间')
  deleted_at    BigInt?                                      /// @db.Comment('删除时间')
  created_by    BigInt?                                      /// @db.Comment('创建人ID')
  updated_by    BigInt?                                      /// @db.Comment('更新人ID')
  sort          Int       @default(0)                        /// @db.Comment('排序')
  
  @@schema("official")
  @@map("case")
  @@index([title], map: "idx_official_case_title")
  @@index([status], map: "idx_official_case_status")
  @@index([created_at(sort: Desc)], map: "idx_official_case_created_at")
  @@index([deleted_at], map: "idx_official_case_deleted_at")
}


// ../apps/official/prisma/models/company_info.prisma
// 公司基础信息表
model company_info {
  id              BigInt    @id @default(autoincrement())
  company_name    String    @db.VarChar(255)
  company_address String?   @db.Text
  logo_url        String?   @db.Text
  banner_url      String?   @db.Text
  vision          String?   @db.Text
  mission         String?   @db.Text
  core_values     String?   @db.Text
  icp_number      String?   @db.VarChar(50)
  copyright       String?   @db.Text
  service_phone   String?   @db.VarChar(20)
  wechat_qrcode   String?   @db.Text
  created_at      BigInt
  updated_at      BigInt
  created_by      BigInt
  updated_by      BigInt
  now_type        Int       @default(1)                        /// @db.Comment('当前选择的模板样式')

  @@schema("official")
}


// ../apps/official/prisma/models/enterprise_information.prisma
/// 企业信息表
model enterprise_information {
  // 主键
  id            BigInt    @id                                /// @db.Comment('ID')
  
  // 基本信息
  title         String    @db.VarChar(100)                   /// @db.Comment('标题')
  description   String?   @db.VarChar(500)                   /// @db.Comment('简介')
  content       String    @db.Text                           /// @db.Comment('内容')
  cate_name     String?   @db.VarChar(100)                   /// @db.Comment('分类名称')
  cover_image   String?   @db.VarChar(255)                   /// @db.Comment('封面图片URL')
  
  // 状态信息
  status        Int       @default(1)                        /// @db.Comment('状态：0-隐藏，1-显示')
  view_count    Int       @default(0)                        /// @db.Comment('浏览次数')

  // 审计字段
  created_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('创建时间')
  updated_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('更新时间')
  deleted_at    BigInt?                                      /// @db.Comment('删除时间')
  created_by    BigInt?                                      /// @db.Comment('创建人ID')
  updated_by    BigInt?                                      /// @db.Comment('更新人ID')
  sort          Int       @default(0)                        /// @db.Comment('排序')
  
  @@schema("official")
  @@map("enterprise_information")
  @@index([title], map: "idx_official_enterprise_information_title")
  @@index([status], map: "idx_official_enterprise_information_status")
  @@index([created_at(sort: Desc)], map: "idx_official_enterprise_information_created_at")
  @@index([deleted_at], map: "idx_official_enterprise_information_deleted_at")
}


// ../apps/official/prisma/models/message_management.prisma
/// 留言表，存储用户留言信息
model OfficialMessageManagement {
  // 主键
  id                BigInt    @id                         /// @db.Comment('留言ID，16位雪花算法')
  
  // 基本信息
  message_location  String    @db.VarChar(100)            /// @db.Comment('留言位置')
  submitter_name    String    @db.VarChar(100)            /// @db.Comment('留言人姓名')
  email             String?   @db.VarChar(100)            /// @db.Comment('邮箱')
  phone             String?   @db.VarChar(20)             /// @db.Comment('电话')
  
  // 留言内容
  message_details   String?   @db.Text                    /// @db.Comment('留言详情')
  attachment_name   String?   @db.VarChar(255)            /// @db.Comment('附件名称')
  attachment_url    String?   @db.VarChar(255)            /// @db.Comment('附件地址')
  
  // 状态信息
  status            Int       @default(0)                 /// @db.Comment('状态：0-未处理，1-已处理')
  
  // 审计字段
  created_at        BigInt                                /// @db.Comment('创建时间戳（毫秒）')
  updated_at        BigInt                                /// @db.Comment('更新时间戳（毫秒）')
  deleted_at        BigInt?                               /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  updated_by        BigInt?                               /// @db.Comment('更新人ID')

  @@map("message_management")
  @@schema("official")
}


// ../apps/official/prisma/models/news/news_articles.prisma
// 官方模块 Prisma Schema 文件
// 定义新闻文章表

// 新闻文章模型定义
/// 新闻文章表，用于存储新闻文章信息
model news_articles {
  // 主键
  id                       BigInt    @id @default(autoincrement())       /// 主键ID
  
  // 基本信息
  title                    String                                        /// 标题
  image                    String                                        /// 商品图片
  summary                  String                                        /// 简介
  news_category_id         BigInt                                        /// 新闻类型ID
  sort_order               Int       @default(0)                         /// 排序
  is_enabled               Int       @default(1) @db.SmallInt            /// 状态：1-启用，0-禁用
  content                  String                                        /// 详情内容
  
  // 时间信息
  created_at               BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// 创建时间戳（毫秒）
  updated_at               BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// 更新时间戳（毫秒）
  deleted_at               BigInt?                                       /// 删除时间戳（毫秒，软删除）
  
  // 操作人信息
  created_by               BigInt                                        /// 创建人ID
  updated_by               BigInt                                        /// 最后更新人ID

  @@schema("official")
  @@index([news_category_id], map: "idx_news_articles_category")
  @@index([is_enabled], map: "idx_news_articles_enabled")
  @@index([deleted_at], map: "idx_news_articles_deleted_at")
  @@index([sort_order], map: "idx_news_articles_sort")
  @@index([created_at], map: "idx_news_articles_created_at")
}


// ../apps/official/prisma/models/news/news_categories.prisma
// 官方模块 Prisma Schema 文件
// 定义新闻分类表

// 新闻分类模型定义
/// 新闻分类表，用于存储新闻分类信息
model news_categories {
  // 主键
  id                       BigInt    @id @default(autoincrement())       /// 主键ID
  

  // 基本信息
  name                     String                                        /// 分类名称
  description              String?                                       /// 分类描述
  

  // 状态信息
  is_enabled               Int       @default(1) @db.SmallInt            /// 是否启用：1-启用，0-禁用
  
  // 时间信息
  created_at               BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// 创建时间戳（毫秒）
  updated_at               BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// 更新时间戳（毫秒）
  deleted_at               BigInt?                                       /// 删除时间戳（毫秒，软删除）
  
  // 操作人信息
  created_by               BigInt?                                       /// 创建人ID
  updated_by               BigInt?                                       /// 最后更新人ID

  @@schema("official")
  @@index([deleted_at], map: "idx_news_categories_deleted_at")

  @@index([is_enabled], map: "idx_news_categories_is_enabled")
}


// ../apps/official/prisma/models/product/product_categories.prisma
// 产品分类表
model product_categories {
  id              BigInt    @id @default(autoincrement())
  name            String    @db.VarChar(255)
  parent_id       BigInt?
  description     String?   @db.Text
  sort_order      Int       @default(0)
  is_enabled      Int       @default(1)
  created_at      BigInt
  updated_at      BigInt
  deleted_at      BigInt?
  created_by      BigInt
  updated_by      BigInt

  // 关联关系
  parent          product_categories?  @relation("ProductCategoryToParent", fields: [parent_id], references: [id])
  children        product_categories[] @relation("ProductCategoryToParent")
  products        products[]           // 添加到产品的反向关系

  @@index([parent_id], name: "idx_product_categories_parent")
  @@index([is_enabled], name: "idx_product_categories_enabled")
  @@index([deleted_at], name: "idx_product_categories_deleted_at")
  @@index([created_at], name: "idx_product_categories_created_at")
  @@schema("official")
}


// ../apps/official/prisma/models/product/products.prisma
// 产品表
model products {
  id              BigInt    @id @default(autoincrement())
  image           String    @db.Text
  name            String    @db.VarChar(255)
  description     String?   @db.Text
  specification   String?   @db.Text
  category_id     BigInt
  is_enabled      Int       @default(1)
  detail_images   String    @db.Text
  created_at      BigInt
  updated_at      BigInt
  deleted_at      BigInt?
  created_by      BigInt
  updated_by      BigInt

  // 关联关系
  category        product_categories  @relation(fields: [category_id], references: [id])

  @@index([category_id], name: "idx_products_category")
  @@index([is_enabled], name: "idx_products_enabled")
  @@index([deleted_at], name: "idx_products_deleted_at")
  @@index([created_at], name: "idx_products_created_at")
  @@schema("official")
}


// ../apps/official/prisma/models/recruitment.prisma
/// 招聘信息表，存储平台招聘信息数据
model Recruitment {
  // 主键
  id            BigInt    @id                                /// @db.Comment('主键ID')
  
  // 基本信息
  title         String    @db.VarChar(100)                   /// @db.Comment('招聘标题')
  recruitment_type Int    @default(1)                        /// @db.Comment('招聘类型:1社招，2校招')
  position_name String    @db.VarChar(100)                   /// @db.Comment('岗位名称')
  position_address String? @db.VarChar(255)                  /// @db.Comment('岗位地址')
  position_duty String?   @db.Text                           /// @db.Comment('岗位职责')
  position_requirement String? @db.Text                      /// @db.Comment('岗位要求')
  position_details String? @db.Text                          /// @db.Comment('岗位详情')
  publish_time  BigInt?                                      /// @db.Comment('发布时间')
  
  // 状态信息
  status        Int       @default(1)                        /// @db.Comment('状态：0-隐藏，1-显示')
  sort          Int       @default(0)                        /// @db.Comment('排序')
  
  // 审计字段
  created_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('创建时间')
  updated_at    BigInt    @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint"))  /// @db.Comment('更新时间')
  deleted_at    BigInt?                                      /// @db.Comment('删除时间')
  created_by    BigInt?                                      /// @db.Comment('创建人ID')
  updated_by    BigInt?                                      /// @db.Comment('更新人ID')
  
  @@schema("official")
  @@map("recruitment")
  @@index([title], map: "idx_recruitment_title")
  @@index([status], map: "idx_recruitment_status")
  @@index([sort], map: "idx_recruitment_sort")
  @@index([created_at(sort: Desc)], map: "idx_recruitment_created_at")
  @@index([deleted_at], map: "idx_recruitment_deleted_at")
}


// ../apps/provider/prisma/models/approval_log.prisma
/// 审批日志表，记录订单审批操作的历史记录
model ProviderApprovalLog {
  // 主键
  id            BigInt    @id                           /// 日志ID，16位雪花算法，系统自动生成
  
  // 关联字段
  operator_id   BigInt                                  /// 操作者ID，关联用户表
  record_id     BigInt                                  /// 关联记录ID（如订单ID）
  
  // 操作信息
  operation_type Int                                    /// 操作类型：1-创建，2-同意发货，3-驳回发货，4-申请开票，5-商务审核通过，6-商务审核驳回，7-财务审核通过，8-财务审核驳回，9-开票完成，10-申请红冲，11-红冲审核通过，12-红冲审核驳回，13-红冲完成
  business_type String?   @db.VarChar(20)               /// 业务类型：order-订单，invoice-开票申请
  remark        String?   @db.Text                      /// 操作备注或说明
  
  // 审计字段
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 索引
  @@index([record_id], name: "idx_approval_log_record_id")
  @@index([operator_id], name: "idx_approval_log_operator_id")
  @@index([operation_type], name: "idx_approval_log_operation_type")
  @@index([business_type], name: "idx_approval_log_business_type")
  @@index([created_at], name: "idx_approval_log_created_at")
  @@index([deleted_at], name: "idx_approval_log_deleted_at")
  
  @@map("approval_log")
  @@schema("provider")
}


// ../apps/provider/prisma/models/goal_setting.prisma
/// 服务商目标设置表，存储成员、部门、公司的业绩目标
model ProviderGoalSetting {
  // 主键
  id            BigInt    @id                           /// 目标设置ID，16位雪花算法，系统自动生成
  
  // 目标基本信息
  target_type   String    @db.VarChar(20)               /// 目标类型：member-成员目标, department-部门目标, company-公司目标
  target_id     BigInt                                  /// 目标对象ID（用户ID、部门ID或公司ID）
  target_name   String    @db.VarChar(100)              /// 目标对象名称
  fiscal_year   Int                                     /// 财年
  goal_type     String    @db.VarChar(20)               /// 目标类型：成交金额、订单数量
  unit          String    @db.VarChar(10)               /// 单位：万、个
  
  // 年度目标
  yearly_target Decimal   @default(0) @db.Decimal(15,2) /// 年度目标
  
  // 季度目标
  q1_target     Decimal   @default(0) @db.Decimal(15,2) /// 第一季度目标
  q2_target     Decimal   @default(0) @db.Decimal(15,2) /// 第二季度目标
  q3_target     Decimal   @default(0) @db.Decimal(15,2) /// 第三季度目标
  q4_target     Decimal   @default(0) @db.Decimal(15,2) /// 第四季度目标
  
  // 月度目标
  m1_target     Decimal   @default(0) @db.Decimal(15,2) /// 1月目标
  m2_target     Decimal   @default(0) @db.Decimal(15,2) /// 2月目标
  m3_target     Decimal   @default(0) @db.Decimal(15,2) /// 3月目标
  m4_target     Decimal   @default(0) @db.Decimal(15,2) /// 4月目标
  m5_target     Decimal   @default(0) @db.Decimal(15,2) /// 5月目标
  m6_target     Decimal   @default(0) @db.Decimal(15,2) /// 6月目标
  m7_target     Decimal   @default(0) @db.Decimal(15,2) /// 7月目标
  m8_target     Decimal   @default(0) @db.Decimal(15,2) /// 8月目标
  m9_target     Decimal   @default(0) @db.Decimal(15,2) /// 9月目标
  m10_target    Decimal   @default(0) @db.Decimal(15,2) /// 10月目标
  m11_target    Decimal   @default(0) @db.Decimal(15,2) /// 11月目标
  m12_target    Decimal   @default(0) @db.Decimal(15,2) /// 12月目标
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  achievements  ProviderGoalAchievement[]               /// 关联的完成情况记录
  
  // 唯一约束
  @@unique([target_type, target_id, fiscal_year, goal_type], name: "unique_goal_setting")
  @@map("goal_setting")
  @@schema("provider")
}

/// 目标完成情况表，记录实际完成情况
model ProviderGoalAchievement {
  // 主键
  id                BigInt    @id                       /// 完成记录ID，16位雪花算法，系统自动生成
  
  // 关联信息
  goal_setting_id   BigInt                              /// 关联的目标设置ID
  achievement_period String   @db.VarChar(20)           /// 完成周期：yearly, q1, q2, q3, q4, m1-m12
  
  // 完成情况
  target_value      Decimal   @default(0) @db.Decimal(15,2) /// 目标值
  actual_value      Decimal   @default(0) @db.Decimal(15,2) /// 实际完成值
  completion_rate   Decimal   @default(0) @db.Decimal(5,2)  /// 完成率（百分比）
  
  // 审计字段
  created_by        BigInt?                             /// 创建者ID，16位
  updated_by        BigInt?                             /// 更新者ID，16位
  created_at        BigInt                              /// 创建时间戳（毫秒）
  updated_at        BigInt                              /// 更新时间戳（毫秒）
  deleted_at        BigInt?                             /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  goal_setting      ProviderGoalSetting @relation(fields: [goal_setting_id], references: [id])
  
  // 唯一约束
  @@unique([goal_setting_id, achievement_period], name: "unique_goal_achievement")
  @@map("goal_achievement")
  @@schema("provider")
}


// ../apps/provider/prisma/models/invoice_application_details.prisma
/// 服务商开票申请详情表，存储开票申请的商品明细信息
model ProviderInvoiceApplicationDetail {
  // 主键
  id                    BigInt    @id                           /// 详情ID，16位雪花算法，系统自动生成
  
  // 关联字段
  application_id        BigInt                                  /// 申请ID，关联invoice_applications表
  
  // 商品信息
  product_name          String    @db.VarChar(200)              /// 商品名称
  product_model         String?   @db.VarChar(100)              /// 规格型号
  unit                  String    @db.VarChar(20)               /// 单位
  quantity              Decimal   @db.Decimal(15,4)             /// 数量
  
  // 价格信息
  unit_price            Decimal   @db.Decimal(15,4)             /// 未税单价
  untaxed_amount        Decimal   @db.Decimal(15,2)             /// 未税金额
  tax_rate              Decimal   @db.Decimal(5,2)              /// 税率（%）
  tax_amount            Decimal   @db.Decimal(15,2)             /// 税额
  taxed_amount          Decimal   @db.Decimal(15,2)             /// 含税金额
  
  // 税收分类
  tax_classification_code String  @db.VarChar(50)               /// 税收分类编码
  tax_classification_name String  @db.VarChar(200)              /// 税收分类名称
  
  // 订单相关信息
  order_detail_id       BigInt?                                 /// 关联的订单详情ID
  sku_code              String?   @db.VarChar(100)              /// SKU编码
  
  // 审计字段
  created_by            BigInt?                                 /// 创建者ID，16位
  updated_by            BigInt?                                 /// 更新者ID，16位
  created_at            BigInt                                  /// 创建时间戳（毫秒）
  updated_at            BigInt                                  /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  application           ProviderInvoiceApplication @relation(fields: [application_id], references: [id])
  
  // 索引
  @@index([application_id], name: "idx_invoice_application_details_app_id")
  @@index([order_detail_id], name: "idx_invoice_application_details_order_detail_id")
  @@index([tax_classification_code], name: "idx_invoice_application_details_tax_code")
  @@index([sku_code], name: "idx_invoice_application_details_sku_code")
  @@index([created_at], name: "idx_invoice_application_details_created_at")
  @@index([deleted_at], name: "idx_invoice_application_details_deleted_at")
  
  @@map("invoice_application_details")
  @@schema("provider")
}


// ../apps/provider/prisma/models/invoice_applications.prisma
/// 服务商开票申请记录表，存储开票申请的基本信息
model ProviderInvoiceApplication {
  // 主键
  id                    BigInt    @id                           /// 申请ID，16位雪花算法，系统自动生成
  
  // 关联字段
  provider_user_id      BigInt                                  /// 申请人ID，关联provider_user表
  order_id              BigInt                                  /// 关联订单ID，关联base.orders表
  invoice_header_id     BigInt                                  /// 发票抬头ID，关联provider_invoice_headers表
  
  // 申请基本信息
  application_no        String    @unique @db.VarChar(50)       /// 申请单号，系统自动生成，唯一
  invoice_type          String    @db.VarChar(20)               /// 发票类型：电子普票、电子专票、纸质普票、纸质专票
  total_amount          Decimal   @db.Decimal(15,2)             /// 申请开票总金额
  tax_amount            Decimal   @db.Decimal(15,2)             /// 税额
  untaxed_amount        Decimal   @db.Decimal(15,2)             /// 未税金额
  
  // 申请状态
  status                Int       @default(1)                   /// 申请状态：1-待商务审核，2-商务审核通过，3-商务审核驳回，4-待财务审核，5-财务审核通过，6-财务审核驳回，7-已开票，8-已红冲
  business_auditor_id   BigInt?                                 /// 商务审核人ID，关联base.system_user表
  business_audit_time   BigInt?                                 /// 商务审核时间戳（毫秒）
  business_audit_remark String?   @db.Text                      /// 商务审核备注
  finance_auditor_id    BigInt?                                 /// 财务审核人ID，关联base.system_user表
  finance_audit_time    BigInt?                                 /// 财务审核时间戳（毫秒）
  finance_audit_remark  String?   @db.Text                      /// 财务审核备注
  
  // 开票信息
  invoice_number        String?   @db.VarChar(50)               /// 发票号码
  invoice_code          String?   @db.VarChar(50)               /// 发票代码
  invoice_date          BigInt?                                 /// 开票日期时间戳（毫秒）
  invoice_file_url      String?   @db.VarChar(500)              /// 发票文件下载地址
  invoicer_id           BigInt?                                 /// 开票人ID，关联base.system_user表
  
  // 红冲相关
  is_red_invoice        Boolean   @default(false)               /// 是否为红冲发票
  original_application_id BigInt?                               /// 原申请ID（红冲时关联原申请）
  red_invoice_reason    String?   @db.Text                      /// 红冲原因
  
  // 申请备注
  remark                String?   @db.Text                      /// 申请备注
  
  // 审计字段
  created_by            BigInt?                                 /// 创建者ID，16位
  updated_by            BigInt?                                 /// 更新者ID，16位
  created_at            BigInt                                  /// 创建时间戳（毫秒）
  updated_at            BigInt                                  /// 更新时间戳（毫秒）
  deleted_at            BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  details               ProviderInvoiceApplicationDetail[]      /// 申请详情
  invoice_header        ProviderInvoiceHeader @relation(fields: [invoice_header_id], references: [id])  /// 发票抬头信息
  
  // 索引
  @@index([provider_user_id], name: "idx_invoice_applications_user_id")
  @@index([order_id], name: "idx_invoice_applications_order_id")
  @@index([invoice_header_id], name: "idx_invoice_applications_header_id")
  @@index([application_no], name: "idx_invoice_applications_no")
  @@index([status], name: "idx_invoice_applications_status")
  @@index([business_auditor_id], name: "idx_invoice_applications_business_auditor")
  @@index([finance_auditor_id], name: "idx_invoice_applications_finance_auditor")
  @@index([invoice_number], name: "idx_invoice_applications_invoice_number")
  @@index([is_red_invoice], name: "idx_invoice_applications_is_red")
  @@index([original_application_id], name: "idx_invoice_applications_original_id")
  @@index([created_at], name: "idx_invoice_applications_created_at")
  @@index([deleted_at], name: "idx_invoice_applications_deleted_at")
  
  @@map("invoice_applications")
  @@schema("provider")
}


// ../apps/provider/prisma/models/order_assignment.prisma
// 订单指派表
model orderAssignment {
  id                  BigInt   @id @default(autoincrement())
  order_id            BigInt   // 订单ID
  order_report_id     BigInt?  // 报备信息ID（可选）
  provider_id         BigInt   // 服务商ID
  salesman_id         BigInt   // 业务员ID
  rate                Decimal  @db.Decimal(10, 4) // 费率
  assignment_amount   Decimal? @db.Decimal(18, 2) // 指派金额
  assignment_status   Int      @default(1) // 指派状态：1-已指派，2-已接受，3-已拒绝，4-已完成
  remark              String?  @db.Text // 备注
  assigned_by         BigInt   // 指派人ID
  assigned_at         BigInt   // 指派时间
  accepted_at         BigInt?  // 接受时间
  completed_at        BigInt?  // 完成时间
  created_at          BigInt   @default(autoincrement())
  updated_at          BigInt   @default(autoincrement())
  deleted_at          BigInt?

  @@map("order_assignment")
  @@schema("provider")
  @@index([order_id], map: "idx_order_assignment_order_id")
  @@index([order_report_id], map: "idx_order_assignment_order_report_id")
  @@index([provider_id], map: "idx_order_assignment_provider_id")
  @@index([salesman_id], map: "idx_order_assignment_salesman_id")
  @@index([assignment_status], map: "idx_order_assignment_status")
  @@index([assigned_at], map: "idx_order_assignment_assigned_at")
  @@index([deleted_at], map: "idx_order_assignment_deleted_at")
}


// ../apps/provider/prisma/models/provider_invoice_headers.prisma
/// 服务商发票抬头表，存储服务商用户创建的发票抬头信息
model ProviderInvoiceHeader {
  // 主键
  id            BigInt    @id                           /// 发票抬头ID，16位雪花算法，系统自动生成
  
  // 关联字段
  provider_user_id BigInt                              /// 服务商用户ID，关联provider_user表
  
  // 发票抬头基本信息
  name          String    @db.VarChar(100)              /// 发票抬头名称，必填
  tax_number    String?   @db.VarChar(50)               /// 纳税人识别号
  phone         String?   @db.VarChar(20)               /// 公司电话
  address       String?   @db.VarChar(200)              /// 公司地址
  
  // 银行信息
  bank_name     String?   @db.VarChar(100)              /// 开户银行名称
  bank_account  String?   @db.VarChar(30)               /// 银行账号
  
  // 状态信息
  is_default    Boolean   @default(false)               /// 是否为默认抬头
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）

  // 关联关系
  applications  ProviderInvoiceApplication[]            /// 使用此抬头的发票申请

  // 索引
  @@index([provider_user_id], name: "idx_provider_invoice_headers_user_id")
  @@index([name], name: "idx_provider_invoice_headers_name")
  @@index([is_default], name: "idx_provider_invoice_headers_is_default")
  @@index([status], name: "idx_provider_invoice_headers_status")
  @@index([deleted_at], name: "idx_provider_invoice_headers_deleted_at")
  
  @@map("provider_invoice_headers")
  @@schema("provider")
}


// ../apps/provider/prisma/models/provider_platform_rate.prisma
/// 服务商平台费率表，存储服务商对各个平台渠道的费率设置
model ProviderPlatformRate {
  // 主键
  id            BigInt    @id                           /// 费率记录ID，16位雪花算法，系统自动生成
  
  // 关联字段
  provider_id   BigInt                                  /// 服务商ID，关联provider_user表
  channel_id    BigInt                                  /// 渠道ID，关联base.channel表
  
  // 费率信息
  rate          Decimal   @default(0.0000) @db.Decimal(8,4) /// 费率，小数格式，如0.0500表示5%
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 唯一约束：同一服务商同一渠道只能有一条费率记录
  @@unique([provider_id, channel_id], name: "unique_provider_channel_rate")
  @@map("provider_platform_rate")
  @@schema("provider")
}


// ../apps/provider/prisma/models/team.prisma
/// 服务商团队信息表，存储团队基本信息
model ProviderTeam {
  // 主键
  id            BigInt    @id                           /// 团队ID，16位雪花算法，系统自动生成
  
  // 基本信息
  name          String    @db.VarChar(100)              /// 团队名称，必填
  description   String?   @db.Text                      /// 团队描述
  leader_id     BigInt                                  /// 团队负责人ID，关联系统用户表
  
  // 目标设置
  monthly_target    Decimal?  @db.Decimal(15,2)         /// 月度目标金额
  quarterly_target  Decimal?  @db.Decimal(15,2)         /// 季度目标金额
  yearly_target     Decimal?  @db.Decimal(15,2)         /// 年度目标金额
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  members       ProviderTeamMember[]                    /// 团队成员关系
  
  @@map("team")
  @@schema("provider")
}

/// 服务商团队成员关联表，存储团队与成员的多对多关系
model ProviderTeamMember {
  // 主键
  id            BigInt    @id                           /// 关联ID，16位雪花算法，系统自动生成
  
  // 关联字段
  team_id       BigInt                                  /// 团队ID，关联团队表
  user_id       BigInt                                  /// 用户ID，关联系统用户表
  
  // 成员角色
  role          String    @default("member") @db.VarChar(20) /// 成员角色：leader-负责人，member-普通成员
  
  // 个人目标设置
  monthly_target    Decimal?  @db.Decimal(15,2)         /// 个人月度目标金额
  quarterly_target  Decimal?  @db.Decimal(15,2)         /// 个人季度目标金额
  yearly_target     Decimal?  @db.Decimal(15,2)         /// 个人年度目标金额
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  join_time     BigInt                                  /// 加入团队时间戳（毫秒）
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）
  
  // 关联关系
  team          ProviderTeam @relation(fields: [team_id], references: [id])
  
  // 唯一约束
  @@unique([team_id, user_id])
  @@map("team_member")
  @@schema("provider")
}


// ../apps/spider/prisma/models/base.prisma





// ../apps/spider/prisma/models/platformData.prisma
/// 平台报备数据类型表，存储平台渠道的报备数据
model platformData {
  // 主键
  id            BigInt    @id /// 平台报备数据ID，雪花ID
  
  // 关联信息
  platform_id   BigInt    /// 平台ID，关联平台表
  store_id      BigInt    /// 店铺ID，关联店铺表
  channel_id    BigInt?   /// 渠道ID，关联渠道表
  
  // 数据信息
  data_type     String    @db.VarChar(50) /// 数据类型，如order、product、customer等
  json_data     Json?     /// JSON格式的数据内容
  
  // 状态信息
  status        Int       @default(1) @db.SmallInt /// 状态：1-有效，0-无效
  
  // 审计字段
  updated_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）

  @@index([platform_id])
  @@index([store_id])
  @@index([channel_id])
  @@index([data_type])
  @@index([platform_id, store_id, data_type])
  @@index([status])
  @@index([updated_at])
  @@map("platform_data")
  @@schema("spider")
}


// ../apps/spider/prisma/models/report_record.prisma
/// 报备记录表，存储平台店铺报备信息
model ReportRecord {
  // 主键
  id            BigInt    @id /// 报备记录ID，雪花ID
  
  // 基本信息
  channel_id    BigInt    /// 渠道ID，关联渠道表
  platform_id   BigInt    /// 平台ID，关联平台表
  store_id      BigInt    /// 店铺ID，关联店铺表
  report_no     String    @db.VarChar(100) /// 报备单号，唯一标识
  
  // 地址信息
  province_id   BigInt?   /// 省份ID，关联地区表
  city_id       BigInt?   /// 城市ID，关联地区表
  district_id   BigInt?   /// 区/县ID，关联地区表
  province      String?   @db.VarChar(50) /// 省份名称
  city          String?   @db.VarChar(50) /// 城市名称
  district      String?   @db.VarChar(50) /// 区/县名称

  // 业务信息
  customer_group_id BigInt? /// 客户组ID，关联客户组表
  customer_group    String? @db.VarChar(100) /// 客户组名称
  
  // 状态信息
  status        Int       @default(1) @db.SmallInt /// 状态：1-正在报备，2-报备成功，3-报备失败
  report_result String?   @db.Text /// 报备结果，存储报备过程中的详细结果信息
  
  // 审计字段
  created_by    BigInt? /// 创建者ID
  updated_by    BigInt? /// 更新者ID
  created_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at    BigInt? /// 删除时间戳（毫秒），空表示未删除

  @@unique([report_no])
  @@index([channel_id])
  @@index([platform_id])
  @@index([store_id])
  @@index([province_id])
  @@index([city_id])
  @@index([district_id])
  @@index([customer_group_id])
  @@index([province, city, district])
  @@index([customer_group])
  @@index([status])
  @@index([created_at])
  @@map("report_record")
  @@schema("spider")
}


// ../apps/spider/prisma/models/spider_log.prisma
/// 爬虫日志表，记录爬虫执行日志信息
model SpiderLog {
  // 主键
  id            BigInt    @id /// 爬虫日志ID，雪花ID
  
  // 关联信息
  task_id       BigInt? /// 关联的爬虫任务ID
  task          SpiderTask? @relation(fields: [task_id], references: [id]) /// 关联的爬虫任务
  spider_id     BigInt? /// 关联的爬虫ID
  store_id      BigInt? /// 关联的店铺ID
  
  // 执行信息
  start_time    BigInt /// 开始执行时间戳（毫秒）
  end_time      BigInt? /// 结束执行时间戳（毫秒）
  status        Int       @default(0) @db.SmallInt /// 执行状态：0-进行中，1-成功，2-失败
  spider_type   String    @default("") @db.VarChar(50) /// 爬虫类型
  
  // 结果信息
  items_count   Int       @default(0) @db.Integer /// 爬取的数据条数
  error_message String?   @db.Text /// 错误信息
  log_content   String?   @db.Text /// 执行日志内容
  
  // 执行详情
  execution_id  String    @db.VarChar(50) /// 执行ID，用于关联具体执行实例
  
  // 审计字段
  created_by    BigInt? /// 创建者ID
  updated_by    BigInt? /// 更新者ID
  created_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at    BigInt? /// 删除时间戳（毫秒），空表示未删除

  @@index([task_id])
  @@index([status])
  @@index([start_time])
  @@map("spider_log")
  @@schema("spider")
}


// ../apps/spider/prisma/models/spider_task.prisma
/// 爬虫任务表，存储爬虫任务配置信息
model SpiderTask {
  // 主键
  id            BigInt    @id /// 爬虫任务ID，雪花ID
  
  // 关联信息
  spider_id     BigInt? /// 爬虫ID
  platform_id   BigInt /// 平台ID
  store_id      BigInt? /// 店铺ID
  spider_type   String    @db.VarChar(50) /// 爬虫类型，如order、product等
  
  // 执行计划
  cron_expression String? @db.VarChar(100) /// Cron表达式，定义任务执行计划
  run_interval  Int? /// 运行间隔（秒）
  
  // 状态信息
  last_run_time BigInt? /// 上次运行时间戳（毫秒）
  next_run_time BigInt? /// 下次计划运行时间戳（毫秒）
  
  // 关联信息
  logs          SpiderLog[] /// 关联的爬虫日志
  
  // 审计字段
  created_by    BigInt? /// 创建者ID
  updated_by    BigInt? /// 更新者ID
  created_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at    BigInt? /// 删除时间戳（毫秒），空表示未删除

  @@index([spider_id])
  @@index([platform_id])
  @@index([store_id])
  @@map("spider_task")
  @@schema("spider")
}


// ../apps/spider/prisma/models/spider.prisma
/// 爬虫表，存储爬虫基本信息
model Spider {
  // 主键
  id            BigInt    @id /// 爬虫ID，雪花ID
  
  // 基本信息
  platform_id   BigInt    /// 平台ID，关联平台表
  name          String    @db.VarChar(100) /// 爬虫名称
  code          String    @db.VarChar(50) /// 爬虫代码，唯一标识
  spider_type   String?   @db.VarChar(255) /// 爬虫类型
  remark        String?   @db.Text /// 备注
  
  // 状态信息
  status        Int       @default(1) @db.SmallInt /// 状态：1-启用，0-禁用
  
  // 审计字段
  created_by    BigInt? /// 创建者ID
  updated_by    BigInt? /// 更新者ID
  created_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间戳（毫秒）
  updated_at    BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间戳（毫秒）
  deleted_at    BigInt? /// 删除时间戳（毫秒），空表示未删除

  @@index([code])
  @@index([status])
  @@index([platform_id])
  @@map("spider")
  @@schema("spider")
}

