/**
 * 发货路由
 * 处理发货相关的路由配置
 * 注意：所有发货相关接口都不需要token验证，可以公开访问
 */
const express = require('express');
const multer = require('multer');
const ShippingController = require('../controllers/ShippingController');

/**
 * 处理文件名编码问题
 * @param {string} filename 原始文件名
 * @returns {string} 处理后的文件名
 */
function fixFilenameEncoding(filename) {
  if (!filename) return filename;

  try {
    // 检查是否包含乱码字符
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/.test(filename)) {
      // 尝试从latin1转换为utf8
      try {
        const buffer = Buffer.from(filename, 'latin1');
        const utf8Name = buffer.toString('utf8');

        // 检查转换后是否包含中文字符
        if (/[\u4e00-\u9fa5]/.test(utf8Name)) {
          return utf8Name;
        }
      } catch (e) {
        // 转换失败，继续尝试其他方法
      }

      // 尝试其他编码方式
      try {
        const bytes = [];
        for (let i = 0; i < filename.length; i++) {
          bytes.push(filename.charCodeAt(i) & 0xFF);
        }
        const buffer = Buffer.from(bytes);
        const utf8Name = buffer.toString('utf8');

        if (/[\u4e00-\u9fa5]/.test(utf8Name)) {
          return utf8Name;
        }
      } catch (e) {
        // 转换失败
      }
    }

    // 如果文件名看起来像URL编码，尝试解码
    if (filename.includes('%')) {
      try {
        const decoded = decodeURIComponent(filename);
        if (decoded !== filename) {
          return decoded;
        }
      } catch (e) {
        // 解码失败，继续使用原文件名
      }
    }

    return filename;
  } catch (error) {
    console.log('文件名编码处理失败:', error);
    return filename;
  }
}

/**
 * 创建发货路由
 * @param {Object} prisma - Prisma 客户端实例
 * @returns {Object} Express 路由实例
 */
function createShippingRouter(prisma) {
  const router = express.Router();

  // 创建控制器实例
  const controller = new ShippingController(prisma);

  // 配置multer中间件，处理中文文件名
  const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
      files: 5 // 最多5个文件
    },
    // 处理文件名编码问题
    fileFilter: (req, file, cb) => {
      // 处理文件名编码问题
      if (file.originalname) {
        const fixedName = fixFilenameEncoding(file.originalname);
        if (fixedName !== file.originalname) {
          console.log('发货路由 - 文件名重新编码:', file.originalname, '->', fixedName);
          file.originalname = fixedName;
        }
      }
      cb(null, true);
    }
  });

  /**
   * 发货相关路由（公开访问，无需认证）
   */

  // 根据订单号获取配送方式列表
  router.get('/delivery-methods/:orderNo', (req, res) => {
    controller.getDeliveryMethodsByOrderNo(req, res);
  });

  // 创建发货信息
  router.post('/shipping-info', (req, res) => {
    controller.createShippingInfo(req, res);
  });

  // 上传文件接口（不需要鉴权）
  router.post('/upload', upload.single('file'), (req, res) => {
    console.log('发货路由 - 进入上传路由处理');

    // 检查是否有文件上传
    if (!req.file) {
      console.log('发货路由 - 未检测到文件');
      return res.status(400).json({
        code: 400,
        message: '未检测到上传文件',
        data: null
      });
    }

    console.log('发货路由 - 调用控制器上传方法');
    // 调用控制器方法处理上传
    controller.uploadFile(req, res);
  });

  return router;
}

module.exports = createShippingRouter;
